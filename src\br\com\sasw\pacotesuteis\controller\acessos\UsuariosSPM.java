/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.controller.acessos;

import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.SASGrupos;
import SasBeans.SasPWFill;
import SasBeans.Saspwac;
import SasBeansCompostas.SaspwacSysdef;
import SasBeansCompostas.UsuarioSatMobWeb;
import SasDaos.AcessosDao;
import SasDaos.FiliaisDao;
import SasDaos.PessoaDao;
import SasDaos.PessoaLoginDao;
import SasDaos.SASGruposDao;
import SasDaos.SasPwFilDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class UsuariosSPM {

    /**
     * Listagem do cadastro de usuarios
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @param satellite
     * @return
     * @throws Exception
     */
    public List<UsuarioSatMobWeb> listagemUsuarios(int primeiro, int linhas, Map filtros, Persistencia persistencia, Persistencia satellite) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.ListaUsuarios(primeiro, linhas, filtros, persistencia, satellite);
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagemAcessos(Map filtros, Persistencia persistencia, Persistencia satellite) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.TotalAcessosMobWeb(filtros, persistencia, satellite);
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("ClientesSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public SasPWFill buscarFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            return saspwfildao.buscaSasPWFill(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Apaga o registro da filial da base de dados
     *
     * @param nome Nome do usuario
     * @param codfilAc
     * @param persistencia Conexão com a base de dados
     * @throws Exception
     */
    public void apagarFilial(String nome, String codfilAc, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessosDAO.apagarRegistroFilial(nome, codfilAc, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<Pessoa> listaPessoaQuery(String query, Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList<>();
        try {
            PessoaDao pessoaDao = new PessoaDao();
            retorno = pessoaDao.listagemPessoaQuery(query, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
        return retorno;
    }

    /**
     * Lista todos os grupos da empresa
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<SASGrupos> listarGrupos(Persistencia persistencia) throws Exception {
        try {
            SASGruposDao sasgruposdao = new SASGruposDao();
            return sasgruposdao.listaSASGrupos(persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca algum usuário vinculado ao código da pessoa
     *
     * @param codPessoaWeb
     * @param persistencia
     * @param satellite
     * @return
     * @throws Exception
     */
    public UsuarioSatMobWeb buscarUsuario(BigDecimal codPessoaWeb, Persistencia persistencia, Persistencia satellite) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.buscarUsuario(codPessoaWeb, persistencia, satellite);
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public Pessoa inserirCodPessoaWeb(Pessoa pessoa, Persistencia local, Persistencia central) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            if (local.getEmpresa().equals(central.getEmpresa())) {
                pessoa.setCodPessoaWEB(pessoa.getCodigo());
                pessoaDao.updateCodPessoaWeb(pessoa, central);
            } else {
                pessoa.setCodPessoaWEB(pessoaDao.inserirPessoaExpressaCentral(pessoa, central));
                pessoaDao.updateCodPessoaWeb(pessoa, local);
            }
            return pessoa;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public Pessoa inserirNovaPessoa(Pessoa pessoa, Persistencia local, Persistencia central) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            pessoa.setCodPessoaWEB(pessoaDao.inserirPessoaExpressaCentral(pessoa, central));
            pessoa.setCodigo(pessoaDao.inserirPessoaExpressa(pessoa, local));
            return pessoa;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public List<SasPWFill> listarFiliaisPermissao(String sNome, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            return saspwfildao.getSasPWFill(sNome, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<PessoaCliAut> listarClientes(BigDecimal codPessoa, Boolean exclusao, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.listaClientes(codPessoa, exclusao, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void apagarCliente(PessoaCliAut cliente, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessosDAO.apagarRegistroCliente(cliente, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<SaspwacSysdef> listarPermissoesUsuario(String nome, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.listaPermissoes(nome, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<PessoaCliAut> buscarClientes(List<SasPWFill> filiais, String query, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.buscaClientesPermissao(filiais, query, persistencia);
        } catch (Exception e) {
            throw new Exception("acessossatmobweb.falhageral<message>" + e.getMessage());
        }
    }

    public List<SaspwacSysdef> buscarPermissoes(String query, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.buscarPermissoes(query, persistencia);
        } catch (Exception e) {
            throw new Exception("acessossatmobweb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Apaga o registro da permissao
     *
     * @param nome Nome do usuario
     * @param sistema código do sistema
     * @param persistencia conexão com a base de dados
     * @throws Exception
     */
    public void apagarPermissoes(String nome, String sistema, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessosDAO.apagarRegistroPermissao(nome, sistema, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * @param usuario UsuarioSatMobWeb a ser cadastrado
     * @param local Conexão com a base de dados local
     * @param central Conexão com a base de dados central
     *
     * @throws Exception
     */
    public void criarAcesso(UsuarioSatMobWeb usuario, Persistencia local, Persistencia central) throws Exception {
        try {
            usuario = (UsuarioSatMobWeb) FuncoesString.removeAcentoObjeto(usuario);
            usuario.getSaspw().setDt_Alter(getDataAtual("SQL"));
            usuario.getSaspw().setHr_Alter(getDataAtual("HORA"));

            usuario.getPessoa().setDt_Alter(getDataAtual("SQL"));
            usuario.getPessoa().setHr_Alter(getDataAtual("HORA"));

            AcessosDao acessosDAO = new AcessosDao();
            PessoaLoginDao pessoalogindao = new PessoaLoginDao();
            PessoaDao pessoadao = new PessoaDao();

            if (acessosDAO.existeUsuario(usuario.getSaspw(), local)) {
                acessosDAO.editarUsuario(usuario.getSaspw(), local);
            } else {
                usuario.getSaspw().setPW(usuario.getPessoa().getPW());
                acessosDAO.criarUsuario(usuario.getSaspw(), local);
            }

            Pessoa pessoa = new Pessoa();
            pessoa.setCodigo(usuario.getSaspw().getCodPessoaWeb());
            pessoa.setNome(usuario.getPessoa().getNome());
            pessoa.setEmail(usuario.getPessoa().getEmail());
            pessoa.setPWWeb(usuario.getPessoa().getPWWeb());
            pessoa.setOperador(usuario.getSaspw().getOperador());
            pessoa.setDt_Alter(getDataAtual("SQL"));
            pessoa.setHr_Alter(getDataAtual("HORA"));
            pessoadao.atualizarUsuario(pessoa, central);
            pessoa.setCodigo(usuario.getSaspw().getCodPessoa());
            pessoadao.atualizarUsuario(pessoa, local);

            if (pessoalogindao.existePessoaLogin(usuario.getPessoalogin(), central)) {
                pessoalogindao.atualizaPessoaLogin(usuario.getPessoalogin(), central);
            } else {
                pessoalogindao.gravaPessoaLogin(usuario.getPessoalogin(), central);
            }
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Cria permissâo individuais para usuário
     *
     * @param permissao Lista contendo informações das permissões
     * @param persistencia Conexão com o banco de dados
     * @exception Exception
     */
    public void criarPermissoesIndividuais(Saspwac permissao, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            permissao = (Saspwac) FuncoesString.removeAcentoObjeto(permissao);
            acessosDAO.criarPermissoesIndividuais(permissao, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Inseri filial no usuario
     *
     * @param filial - Objeto contendo informações sobre
     * @param persistencia Conexão com base de dados
     * @throws Exception
     */
    public void inserirFilial(SasPWFill filial, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            filial = (SasPWFill) FuncoesString.removeAcentoObjeto(filial);
            if (!acessosDAO.existeFilialUsuario(filial.getNome(), filial.getCodFil(), filial.getCodfilAc(), persistencia)) {
                acessosDAO.inserirFilial(filial, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirCliente(PessoaCliAut cliente, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            Boolean existeCliente = acessosDAO.existeCliente(cliente, persistencia);
            if (existeCliente) {
                acessosDAO.atualizaCliente(cliente, persistencia);
            } else {
                acessosDAO.inserirCliente(cliente, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }
}
