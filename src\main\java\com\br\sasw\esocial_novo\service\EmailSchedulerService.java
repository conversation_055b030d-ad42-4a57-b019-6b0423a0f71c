package com.br.sasw.esocial_novo.service;

import com.br.sasw.esocial_novo.config.EmailConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Serviço de agendamento para processamento automático de emails
 * Substitui a Thread manual do projeto antigo por um scheduler Spring moderno
 */
@Service
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "email.scheduler-enabled", havingValue = "true", matchIfMissing = true)
public class EmailSchedulerService {

    private final EmailService emailService;
    private final EmailConfig emailConfig;
    
    // Flag para controlar se o processamento está ativo
    private final AtomicBoolean processandoEmails = new AtomicBoolean(false);
    private final AtomicBoolean schedulerAtivo = new AtomicBoolean(true);

    /**
     * Processa emails pendentes a cada intervalo configurado
     * Equivale ao loop while(EmailAtivo) do projeto antigo, mas usando @Scheduled
     */
    @Scheduled(fixedDelayString = "#{@emailConfig.schedulerIntervalMs}")
    public void processarEmailsAgendado() {
        if (!schedulerAtivo.get()) {
            log.debug("Scheduler de emails está desativado");
            return;
        }

        // Evita execuções concorrentes
        if (!processandoEmails.compareAndSet(false, true)) {
            log.debug("Processamento de emails já está em andamento, pulando execução");
            return;
        }

        try {
            log.debug("Iniciando processamento agendado de emails");
            emailService.processarEmailsPendentes();
            
        } catch (Exception e) {
            log.error("Erro no processamento agendado de emails: {}", e.getMessage(), e);
            
        } finally {
            processandoEmails.set(false);
            log.debug("Processamento agendado de emails finalizado");
        }
    }

    /**
     * Processa emails GSI separadamente (se necessário)
     * Baseado na lógica específica para SATPROSECUR e SATGLOVAL
     */
    @Scheduled(fixedDelayString = "#{@emailConfig.schedulerIntervalMs}")
    public void processarEmailsGSI() {
        if (!schedulerAtivo.get()) {
            return;
        }

        try {
            // Implementar lógica específica para emails GSI se necessário
            // Por enquanto, a lógica está integrada no EmailService principal
            
        } catch (Exception e) {
            log.error("Erro no processamento de emails GSI: {}", e.getMessage(), e);
        }
    }

    /**
     * Para o scheduler (equivale ao EmailAtivo = false do projeto antigo)
     */
    public void pararScheduler() {
        log.info("Parando scheduler de emails");
        schedulerAtivo.set(false);
        
        // Aguarda o processamento atual terminar
        while (processandoEmails.get()) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        log.info("Scheduler de emails parado");
    }

    /**
     * Inicia o scheduler (equivale ao EmailAtivo = true do projeto antigo)
     */
    public void iniciarScheduler() {
        log.info("Iniciando scheduler de emails");
        schedulerAtivo.set(true);
    }

    /**
     * Verifica se o scheduler está ativo
     */
    public boolean isSchedulerAtivo() {
        return schedulerAtivo.get();
    }

    /**
     * Verifica se está processando emails no momento
     */
    public boolean isProcessandoEmails() {
        return processandoEmails.get();
    }

    /**
     * Força o processamento imediato de emails (para testes ou controle manual)
     */
    public void processarEmailsImediato() {
        if (processandoEmails.get()) {
            throw new IllegalStateException("Já existe um processamento de emails em andamento");
        }

        log.info("Iniciando processamento manual de emails");
        
        try {
            processandoEmails.set(true);
            emailService.processarEmailsPendentes();
            log.info("Processamento manual de emails concluído");
            
        } catch (Exception e) {
            log.error("Erro no processamento manual de emails: {}", e.getMessage(), e);
            throw e;
            
        } finally {
            processandoEmails.set(false);
        }
    }

    /**
     * Retorna status do scheduler
     */
    public SchedulerStatus getStatus() {
        return new SchedulerStatus(
            schedulerAtivo.get(),
            processandoEmails.get(),
            emailConfig.getSchedulerIntervalMs(),
            emailService.getEmailStats()
        );
    }

    /**
     * Record para status do scheduler
     */
    public record SchedulerStatus(
        boolean ativo,
        boolean processando,
        long intervalMs,
        EmailService.EmailStats emailStats
    ) {}
}
