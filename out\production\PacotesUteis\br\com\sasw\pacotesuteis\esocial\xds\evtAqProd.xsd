﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtAqProd/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtAqProd/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtAqProd">
                    <xs:annotation>
                        <xs:documentation>Evento Aquisicao de Producao</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeEveFopagMensal">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="infoAquisProd">
                                <xs:annotation>
                                    <xs:documentation>Informacao da Aquisicao de Producao</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="ideEstabAdquir">
                                            <xs:annotation>
                                                <xs:documentation>Identificacao do estabelecimento adquirente da producao</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="tpInscAdq">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Tipo de inscricao do adquirente, conforme tabela 05</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="nrInscAdq">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Indicar o número de inscricao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="\d{14}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="tpAquis" maxOccurs="6">
                                                        <xs:annotation>
                                                            <xs:documentation>Aquisicao de producao</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="ideProdutor" maxOccurs="9999">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Identificacao dos produtores rurais</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="nfs" minOccurs="0" maxOccurs="9999">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Notas Fiscais da aquisicao de producao</xs:documentation>
                                                                                </xs:annotation>
                                                                                <xs:complexType>
                                                                                    <xs:sequence>
                                                                                    </xs:sequence>
                                                                                    <xs:attribute name="serie" type="xs:string"/>
                                                                                    <xs:attribute name="nrDocto" type="xs:string" use="required"/>
                                                                                    <xs:attribute name="dtEmisNF" type="xs:date" use="required"/>
                                                                                    <xs:attribute name="vlrBruto" type="xs:decimal" use="required"/>
                                                                                    <xs:attribute name="vrCPDescPR" type="xs:decimal" use="required"/>
                                                                                    <xs:attribute name="vrRatDescPR" type="xs:decimal" use="required"/>
                                                                                    <xs:attribute name="vrSenarDesc" type="xs:decimal" use="required"/>
                                                                                </xs:complexType>
                                                                            </xs:element>
                                                                            <xs:element name="infoProcJud" minOccurs="0" maxOccurs="10">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Informacao de Processo Judicial do produtor</xs:documentation>
                                                                                </xs:annotation>
                                                                                <xs:complexType>
                                                                                    <xs:sequence>
                                                                                    </xs:sequence>
                                                                                    <xs:attribute name="nrProcJud" type="xs:string" use="required"/>
                                                                                    <xs:attribute name="codSusp" type="xs:integer" use="required"/>
                                                                                    <xs:attribute name="vrCPNRet" type="xs:decimal" use="required"/>
                                                                                    <xs:attribute name="vrRatNRet" type="xs:decimal" use="required"/>
                                                                                    <xs:attribute name="vrSenarNRet" type="xs:decimal" use="required"/>
                                                                                </xs:complexType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                        <xs:attribute name="tpInscProd" type="xs:byte" use="required"/>
                                                                        <xs:attribute name="nrInscProd" type="xs:string" use="required"/>
                                                                        <xs:attribute name="vlrBruto" type="xs:decimal" use="required"/>
                                                                        <xs:attribute name="vrCPDescPR" type="xs:decimal" use="required"/>
                                                                        <xs:attribute name="vrRatDescPR" type="xs:decimal" use="required"/>
                                                                        <xs:attribute name="vrSenarDesc" type="xs:decimal" use="required"/>
                                                                        <xs:attribute name="indOpcCP" type="xs:byte" use="required"/>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                                <xs:element name="infoProcJ" minOccurs="0" maxOccurs="20">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Informacoes de Processo Judicial comum a todos os produtores</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                        </xs:sequence>
                                                                        <xs:attribute name="nrProcJud" type="xs:string" use="required"/>
                                                                        <xs:attribute name="codSusp" type="xs:integer" use="required"/>
                                                                        <xs:attribute name="vrCPNRet" type="xs:decimal" use="required"/>
                                                                        <xs:attribute name="vrRatNRet" type="xs:decimal" use="required"/>
                                                                        <xs:attribute name="vrSenarNRet" type="xs:decimal" use="required"/>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                            <xs:attribute name="indAquis" type="xs:byte" use="required"/>
                                                            <xs:attribute name="vlrTotAquis" type="xs:decimal" use="required"/>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeEveFopagMensal">
        <xs:annotation>
            <xs:documentation>Identificacao do Evento PeriOdico</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="indRetif">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Retificacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrRecibo" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do recibo do arquivo a ser retificado</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="40"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="indApuracao">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de periodo de apuracao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="perApur">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo ao qual se referem as informacoes</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
