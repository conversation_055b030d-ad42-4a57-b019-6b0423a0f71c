﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtCdBenPrRP/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtCdBenPrRP/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtCdBenPrRP">
                    <xs:annotation>
                        <xs:documentation>Evento de cadastro de beneficios previdenciarios de Regimes PrOprios</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeEveTrab">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmprPJ">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideBenef">
                                <xs:annotation>
                                    <xs:documentation>Identificacao do beneficiario</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="cpfBenef">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>CPF do beneficiario</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="\d{11}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nmBenefic">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Nome do Beneficiario</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="2"/>
                                                    <xs:maxLength value="70"/>
                                                    <xs:whiteSpace value="preserve"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="dadosBenef" type="TDadosBenef">
                                            <xs:annotation>
                                                <xs:documentation>Dados do beneficiario</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="infoBeneficio">
                                <xs:annotation>
                                    <xs:documentation>Informacoes relacionadas ao beneficio previdenciario</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="tpPlanRP">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Tipo de plano de segregacao da massa</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="iniBeneficio" type="TDadosBeneficio" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes relativas a beneficios previdenciarios - Inicio</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="altBeneficio" type="TDadosBeneficio" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes relativas a beneficios previdenciarios - Alteracao</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="fimBeneficio" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes relativas a beneficios previdenciarios - Término</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="tpBenef">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Tipo de beneficio previdenciario, conforme tabela 25</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d{1,2}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="nrBenefic">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Número do beneficio</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="2"/>
                                                                <xs:maxLength value="20"/>
                                                                <xs:whiteSpace value="preserve"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="dtFimBenef">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Data de término</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:date">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="mtvFim">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Motivo</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d{1,2}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeEveTrab">
        <xs:annotation>
            <xs:documentation>Identificacao do evento</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="indRetif">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Retificacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrRecibo" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do recibo do arquivo a ser retificado</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="40"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmprPJ">
        <xs:annotation>
            <xs:documentation>Informacoes do Empregador PJ</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TDadosBenef">
        <xs:annotation>
            <xs:documentation>Dados do beneficiario</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="dadosNasc">
                <xs:annotation>
                    <xs:documentation>Informacoes de nascimento do beneficiario</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="dtNascto">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Data de Nascimento</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:date">
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="codMunic" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>COdigo do Municipio</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:integer">
                                    <xs:pattern value="\d{7}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="uf" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Sigla da UF</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:enumeration value="AC"/>
                                    <xs:enumeration value="AL"/>
                                    <xs:enumeration value="AP"/>
                                    <xs:enumeration value="AM"/>
                                    <xs:enumeration value="BA"/>
                                    <xs:enumeration value="CE"/>
                                    <xs:enumeration value="DF"/>
                                    <xs:enumeration value="ES"/>
                                    <xs:enumeration value="GO"/>
                                    <xs:enumeration value="MA"/>
                                    <xs:enumeration value="MT"/>
                                    <xs:enumeration value="MS"/>
                                    <xs:enumeration value="MG"/>
                                    <xs:enumeration value="PA"/>
                                    <xs:enumeration value="PB"/>
                                    <xs:enumeration value="PR"/>
                                    <xs:enumeration value="PE"/>
                                    <xs:enumeration value="PI"/>
                                    <xs:enumeration value="RJ"/>
                                    <xs:enumeration value="RN"/>
                                    <xs:enumeration value="RS"/>
                                    <xs:enumeration value="RO"/>
                                    <xs:enumeration value="RR"/>
                                    <xs:enumeration value="SC"/>
                                    <xs:enumeration value="SP"/>
                                    <xs:enumeration value="SE"/>
                                    <xs:enumeration value="TO"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="paisNascto">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>COdigo do Pais de Nascimento</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:length value="3"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="paisNac">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>COdigo do pais de nacionalidade do trabalhador</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:length value="3"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nmMae" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Nome da mae do trabalhador</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="70"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nmPai" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Nome do pai do trabalhador</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="70"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="endereco">
                <xs:annotation>
                    <xs:documentation>Endereco do Trabalhador</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:choice>
                            <xs:element name="brasil" type="TEnderecoBrasil">
                                <xs:annotation>
                                    <xs:documentation>Endereco no Brasil</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="exterior" type="TEnderecoExterior">
                                <xs:annotation>
                                    <xs:documentation>Endereco no Exterior</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:choice>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEnderecoBrasil">
        <xs:annotation>
            <xs:documentation>Informacoes do Endereco no Brasil</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpLograd">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="4"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dscLograd">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Descricao do logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="100"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrLograd">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="10"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="complemento" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Complemento do logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="30"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="bairro" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Nome do bairro/distrito</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="90"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="cep">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo de Enderecamento Postal - CEP</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="codMunic">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo do Municipio</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:integer">
                        <xs:pattern value="\d{7}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="uf">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Sigla da UF</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="AC"/>
                        <xs:enumeration value="AL"/>
                        <xs:enumeration value="AP"/>
                        <xs:enumeration value="AM"/>
                        <xs:enumeration value="BA"/>
                        <xs:enumeration value="CE"/>
                        <xs:enumeration value="DF"/>
                        <xs:enumeration value="ES"/>
                        <xs:enumeration value="GO"/>
                        <xs:enumeration value="MA"/>
                        <xs:enumeration value="MT"/>
                        <xs:enumeration value="MS"/>
                        <xs:enumeration value="MG"/>
                        <xs:enumeration value="PA"/>
                        <xs:enumeration value="PB"/>
                        <xs:enumeration value="PR"/>
                        <xs:enumeration value="PE"/>
                        <xs:enumeration value="PI"/>
                        <xs:enumeration value="RJ"/>
                        <xs:enumeration value="RN"/>
                        <xs:enumeration value="RS"/>
                        <xs:enumeration value="RO"/>
                        <xs:enumeration value="RR"/>
                        <xs:enumeration value="SC"/>
                        <xs:enumeration value="SP"/>
                        <xs:enumeration value="SE"/>
                        <xs:enumeration value="TO"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEnderecoExterior">
        <xs:annotation>
            <xs:documentation>Informacoes do Endereco no Exterior</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="paisResid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo do Pais</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:length value="3"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dscLograd">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Descricao do logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="100"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrLograd">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="10"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="complemento" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Complemento do logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="30"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="bairro" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Nome do bairro/distrito</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="90"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nmCid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Nome da Cidade</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="50"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="codPostal" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo de Enderecamento Postal</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="4"/>
                        <xs:maxLength value="12"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TDadosBeneficio">
        <xs:annotation>
            <xs:documentation>Dados do beneficio previdenciario</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpBenef">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de beneficio previdenciario, conforme tabela 25</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d{1,2}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrBenefic">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do beneficio</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="20"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dtIniBenef">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Data de inicio do beneficio previdenciario</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="vrBenef">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Valor do beneficio previdenciario</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="14"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="999999999999"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="infoPenMorte" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Informacoes relativas a pensao por morte</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="idQuota">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Identificacao da quota do beneficio</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="30"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="cpfInst">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>CPF do instituidor da pensao por morte</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:pattern value="\d{11}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
