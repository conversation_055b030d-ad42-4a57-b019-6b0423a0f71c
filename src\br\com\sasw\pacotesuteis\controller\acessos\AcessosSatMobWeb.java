package br.com.sasw.pacotesuteis.controller.acessos;

import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Filiais;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.PessoaLogin;
import SasBeans.PessoaPortalSrv;
import SasBeans.SASGrupos;
import SasBeans.SasPWFill;
import SasBeans.Saspw;
import SasBeans.Saspwac;
import SasBeans.Sysdef;
import SasBeansCompostas.SaspwacSysdef;
import SasBeansCompostas.UsuarioSatMobWeb;
import SasDaos.AcessosDao;
import SasDaos.ClientesDao;
import SasDaos.FiliaisDao;
import SasDaos.PessoaDao;
import SasDaos.PessoaLoginDao;
import SasDaos.PessoaPortalSrvDao;
import SasDaos.SASGruposDao;
import SasDaos.SasPwFilDao;
import SasDaos.SaspwDao;
import br.com.sasw.pacotesuteis.sasbeans.PortalSrv;
import br.com.sasw.pacotesuteis.sasdaos.PortalSrvDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR> Maracaipe
 */
public class AcessosSatMobWeb {

    public Pessoa inserirNovaPessoa(Pessoa pessoa, Persistencia local, Persistencia central) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            List<Pessoa> pessoasCentral = pessoaDao.buscarPessoaCPFEmail(pessoa.getEmail(), pessoa.getCPF(), central);
            List<Pessoa> pessoasLocal = pessoaDao.buscarPessoaCPFEmail(pessoa.getEmail(), pessoa.getCPF(), local);

            String cpf = pessoa.getCPF();
            String email = pessoa.getEmail();

            // Se não achar nada nas duas bases, insere normalmente.           
            if (pessoasCentral.isEmpty() && pessoasLocal.isEmpty()) {
                pessoa.setCodPessoaWEB(pessoaDao.inserirPessoa(pessoa, central));
                pessoa.setCodigo(pessoaDao.inserirPessoa(pessoa, local));
            } else {

                // Se achar email mas não achar CPF, email em uso
                for (Pessoa p : pessoasCentral) {
                    if (email.equals(p.getEmail()) && !cpf.equals(p.getCPF())) {
                        throw new Exception("EmailEmUso");
                    }
                }
                for (Pessoa p : pessoasLocal) {
                    if (email.equals(p.getEmail()) && !cpf.equals(p.getCPF())) {
                        throw new Exception("EmailEmUso");
                    }
                }

                // Se achar o CPF,
                pessoa.setCodPessoaWEB(pessoaDao.inserirPessoa(pessoa, central));
                pessoa.setCodigo(pessoaDao.inserirPessoa(pessoa, local));
            }

            return pessoa;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista de acesso
     *
     * @param codfil Código da filial
     * @param persistencia Conexao com a base de dados
     * @return lista contendo os registros dos acessos
     * @throws Exception
     */
    public List<Saspw> listaAcessos(BigDecimal codfil, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            return acessosDAO.listaUsuarios(codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista de acesso
     *
     * @param codfil Código da filial
     * @param persistencia Conexao com a base de dados
     * @return lista contendo os registros dos acessos
     * @throws Exception
     */
    public Integer contaAcessos(BigDecimal codfil, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            return acessosDAO.contaUsuarios(codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista de acesso
     *
     * @param acesso Acesso com os parametros a serem pesquisados
     * @param codPessoa
     * @param persistencia Conexao com a base de dados
     * @return lista contendo os registros dos acessos
     * @throws Exception
     */
    public List<Saspw> pesquisaAcessos(Saspw acesso, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        List<Saspw> acessos = new ArrayList();
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessos = acessosDAO.pesquisaUsuarios(acesso, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
        return acessos;
    }

    /**
     * Inseri filial no usuario
     *
     * @param filial - Objeto contendo informações sobre
     * @param persistencia Conexão com base de dados
     * @throws Exception
     */
    public void inserirFilial(SasPWFill filial, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            filial = (SasPWFill) FuncoesString.removeAcentoObjeto(filial);
            filial.setDt_Alter(DataAtual.getDataAtual("SQL"));
            filial.setHr_Alter(DataAtual.getDataAtual("HORA"));
            if (!acessosDAO.existeFilialUsuario(filial.getNome(), filial.getCodFil(), filial.getCodfilAc(), persistencia)) {
                acessosDAO.inserirFilial(filial, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirCliente(PessoaCliAut cliente, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            Boolean existeCliente = acessosDAO.existeCliente(cliente, persistencia);
            if (existeCliente) {
                acessosDAO.atualizaCliente(cliente, persistencia);
            } else {
                acessosDAO.inserirCliente(cliente, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirServicoAutomatico(PessoaPortalSrv pessoaPortalSrv, Persistencia persistencia) throws Exception {
        try {
            PessoaPortalSrvDao pessoaPortalSrvDao = new PessoaPortalSrvDao();
            pessoaPortalSrvDao.inserirServicoAutomatico(pessoaPortalSrv, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirServico(PessoaPortalSrv pessoaPortalSrv, Persistencia persistencia) throws Exception {
        try {
            PessoaPortalSrvDao pessoaPortalSrvDao = new PessoaPortalSrvDao();
            pessoaPortalSrvDao.inserirServico(pessoaPortalSrv, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * @param usuario UsuarioSatMobWeb a ser cadastrado
     * @param local Conexão com a base de dados local
     * @param central Conexão com a base de dados central
     *
     * @throws Exception
     */
    public void criarAcesso(UsuarioSatMobWeb usuario, Persistencia local, Persistencia central) throws Exception {
        try {
            usuario = (UsuarioSatMobWeb) FuncoesString.removeAcentoObjeto(usuario);
            usuario.getSaspw().setDt_Alter(DataAtual.getDataAtual("SQL"));
            usuario.getSaspw().setHr_Alter(DataAtual.getDataAtual("HORA"));

            usuario.getPessoa().setDt_Alter(DataAtual.getDataAtual("SQL"));
            usuario.getPessoa().setHr_Alter(DataAtual.getDataAtual("HORA"));

            AcessosDao acessosDAO = new AcessosDao();
            PessoaLoginDao pessoalogindao = new PessoaLoginDao();
            PessoaDao pessoadao = new PessoaDao();

            if (acessosDAO.existeUsuario(usuario.getSaspw(), local)) {
                acessosDAO.editarUsuario(usuario.getSaspw(), local);
            } else {
                usuario.getSaspw().setPW(usuario.getPessoa().getPW());
                acessosDAO.criarUsuario(usuario.getSaspw(), local);
            }

            Pessoa pessoa = new Pessoa();
            pessoa.setCodigo(usuario.getSaspw().getCodPessoaWeb());
            pessoa.setPWWeb(usuario.getPessoa().getPWWeb());
            pessoa.setOperador(usuario.getSaspw().getOperador());
            pessoa.setDt_Alter(getDataAtual("SQL"));
            pessoa.setHr_Alter(getDataAtual("HORA"));
            pessoadao.atualizaSenhaSatMob(pessoa, central);
            pessoa.setCodigo(usuario.getSaspw().getCodPessoa());
            pessoadao.atualizaSenhaSatMob(pessoa, local);

            if (pessoalogindao.existePessoaLogin(usuario.getPessoalogin(), central)) {
                pessoalogindao.atualizaPessoaLogin(usuario.getPessoalogin(), central);
            } else {
                pessoalogindao.gravaPessoaLogin(usuario.getPessoalogin(), central);
            }
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public Pessoa criarAcessoEW(UsuarioSatMobWeb usuario, Persistencia local, Persistencia central) throws Exception {
        try {

            AcessosDao acessosDAO = new AcessosDao();
            PessoaLoginDao pessoalogindao = new PessoaLoginDao();
            PessoaDao pessoaDao = new PessoaDao();

//            if(pessoaDao.verificaEmail(usuario.getPessoa(), local) || pessoaDao.verificaEmail(usuario.getPessoa(), central))
//                throw new Exception("EmailCadastrado");
            /**
             * Cadastro expresso na base central
             */
            String codPessoaWEB = pessoaDao.inserirPessoaExpressa(usuario.getPessoa(), central);
            if (codPessoaWEB.equals("0")) {
                throw new Exception("ErroCadastroPessoa");
            }

            usuario.getPessoa().setCodPessoaWEB(new BigDecimal(codPessoaWEB));

            /**
             * Cadastro expresso na base local
             */
            String codPessoa = pessoaDao.inserirPessoaExpressa(usuario.getPessoa(), local);
            if (codPessoa.equals("0")) {
                throw new Exception("ErroCadastroPessoa");
            }

            usuario.getPessoa().setCodigo(codPessoa);

            usuario.getSaspw().setNome(usuario.getPessoa().getCodigo().toBigInteger().toString());
            usuario.getSaspw().setCodPessoa(usuario.getPessoa().getCodigo().toBigInteger().toString());
            usuario.getSaspw().setCodPessoaWeb(usuario.getPessoa().getCodPessoaWEB().toPlainString());
            usuario.getSaspw().setNomeCompleto(usuario.getPessoa().getNome().toUpperCase());
            usuario.getSaspw().setCodigo(usuario.getPessoa().getCodigo().toBigInteger().toString());
            usuario.getPessoalogin().setCodigo(usuario.getPessoa().getCodPessoaWEB());
            usuario.getPessoalogin().setCodPessoaBD(usuario.getPessoa().getCodigo());

            usuario = (UsuarioSatMobWeb) FuncoesString.removeAcentoObjeto(usuario);
            usuario.getSaspw().setDt_Alter(DataAtual.getDataAtual("SQL"));
            usuario.getSaspw().setHr_Alter(DataAtual.getDataAtual("HORA"));

            usuario.getPessoa().setDt_Alter(DataAtual.getDataAtual("SQL"));
            usuario.getPessoa().setHr_Alter(DataAtual.getDataAtual("HORA"));

            if (acessosDAO.existeUsuario(usuario.getSaspw(), local)) {
                acessosDAO.editarUsuario(usuario.getSaspw(), local);
            } else {
                usuario.getSaspw().setPW(usuario.getPessoa().getPW());
                acessosDAO.criarUsuario(usuario.getSaspw(), local);
            }

            Pessoa pessoa = new Pessoa();
            pessoa.setCodigo(usuario.getSaspw().getCodPessoaWeb());
            pessoa.setPWWeb(usuario.getPessoa().getPWWeb());
            pessoa.setOperador(usuario.getSaspw().getOperador());
            pessoa.setDt_Alter(getDataAtual("SQL"));
            pessoa.setHr_Alter(getDataAtual("HORA"));
            pessoaDao.atualizaSenhaSatMob(pessoa, central);
            pessoa.setCodigo(usuario.getSaspw().getCodPessoa());
            pessoaDao.atualizaSenhaSatMob(pessoa, local);

            if (pessoalogindao.existePessoaLogin(usuario.getPessoalogin(), central)) {
                pessoalogindao.atualizaPessoaLogin(usuario.getPessoalogin(), central);
            } else {
                pessoalogindao.gravaPessoaLogin(usuario.getPessoalogin(), central);
            }

            return usuario.getPessoa();
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void trocarSenhaCliente(BigDecimal codPessoa, String senha, String operador, Persistencia local, Persistencia central) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            Pessoa pessoa = new Pessoa();
            pessoa.setCodigo(codPessoa);
            Pessoa temp = pessoadao.BuscaPessoa(pessoa, local).get(0);
            pessoa.setOperador(operador);
            pessoa.setPWWeb(senha);
            pessoa.setDt_Alter(getDataAtual("SQL"));
            pessoa.setHr_Alter(getDataAtual("HORA"));
            pessoadao.atualizaSenhaSatMob(pessoa, local);
            pessoa.setCodigo(temp.getCodPessoaWEB());
            pessoadao.atualizaSenhaSatMob(pessoa, central);
        } catch (Exception e) {
            throw new Exception("login.falhaalterarsenhacliente<message>" + e.getMessage());
        }
    }

    /**
     * Edita acesso e atualiza a senha da pessoa nas bases local e central;
     *
     * @param acesso Obejto Saspw
     * @param local Conexão com a base de dados local
     * @param central Conexão com a base de dados central
     * @throws Exception
     */
    public void editarAcesso(UsuarioSatMobWeb acesso, Persistencia local, Persistencia central) throws Exception {
        try {
            // acesso.setPW(SatCripto.Criptografar(acesso.getPW(), "Active Solutions SAS Systems"));
            acesso.getSaspw().setDt_Alter(DataAtual.getDataAtual("SQL"));
            acesso.getSaspw().setHr_Alter(DataAtual.getDataAtual("HORA"));

            acesso.getPessoa().setDt_Alter(DataAtual.getDataAtual("SQL"));
            acesso.getPessoa().setHr_Alter(DataAtual.getDataAtual("HORA"));

            AcessosDao acessosDAO = new AcessosDao();
            acessosDAO.editarUsuario(acesso.getSaspw(), local);

            PessoaDao pessoadao = new PessoaDao();
            Pessoa pessoa = new Pessoa();
            pessoa.setOperador(acesso.getSaspw().getOperador());
            pessoa.setCodigo(acesso.getSaspw().getCodPessoaWeb());
            pessoa.setPWWeb(acesso.getPessoa().getPWWeb());
            pessoadao.atualizaSenhaSatMob(pessoa, central);
            pessoa.setDt_Alter(getDataAtual("SQL"));
            pessoa.setHr_Alter(getDataAtual("HORA"));
            pessoa.setCodigo(acesso.getSaspw().getCodPessoa());
            pessoadao.atualizaSenhaSatMob(pessoa, local);

            PessoaLoginDao pessoalogindao = new PessoaLoginDao();
            pessoalogindao.atualizaPessoaLogin(acesso.getPessoalogin(), central);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void editarAcessoPessoa(UsuarioSatMobWeb acesso, Persistencia local, Persistencia central) throws Exception {
        try {
            // acesso.setPW(SatCripto.Criptografar(acesso.getPW(), "Active Solutions SAS Systems"));
            acesso.getSaspw().setDt_Alter(DataAtual.getDataAtual("SQL"));
            acesso.getSaspw().setHr_Alter(DataAtual.getDataAtual("HORA"));

            acesso.getPessoa().setDt_Alter(DataAtual.getDataAtual("SQL"));
            acesso.getPessoa().setHr_Alter(DataAtual.getDataAtual("HORA"));

            AcessosDao acessosDAO = new AcessosDao();
            acessosDAO.editarUsuario(acesso.getSaspw(), local);

            PessoaDao pessoadao = new PessoaDao();
            Pessoa pessoa = new Pessoa();
            pessoa.setOperador(acesso.getSaspw().getOperador());
            pessoa.setCodigo(acesso.getSaspw().getCodPessoaWeb());
            pessoa.setPWWeb(acesso.getPessoa().getPWWeb());
            pessoadao.atualizaSenhaSatMob(pessoa, central);
            pessoa.setDt_Alter(getDataAtual("SQL"));
            pessoa.setHr_Alter(getDataAtual("HORA"));
            pessoa.setCodigo(acesso.getSaspw().getCodPessoa());
            pessoadao.atualizaSenhaSatMob(pessoa, local);
            pessoadao.atualizaNomeEmail(acesso.getPessoa(), local);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Cria permissâo individuais para usuário
     *
     * @param permissao Lista contendo informações das permissões
     * @param persistencia Conexão com o banco de dados
     * @exception Exception
     */
    public void criarPermissoesIndividuais(Saspwac permissao, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            permissao = (Saspwac) FuncoesString.removeAcentoObjeto(permissao);
            acessosDAO.criarPermissoesIndividuais(permissao, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Edita permissões individuais
     *
     * @param permissao permissão que deseja editar
     * @param persistencia Conexao com o banco de dados
     * @throws Exception
     */
    public void editarPermissoesIndividuais(Saspwac permissao, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            permissao = (Saspwac) FuncoesString.removeAcentoObjeto(permissao);
            acessosDAO.editarPermissoesIndividuais(permissao, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Apaga o registro da filial da base de dados
     *
     * @param nome Nome do usuario
     * @param codfilAc
     * @param persistencia Conexão com a base de dados
     * @throws Exception
     */
    public void apagarFilial(String nome, String codfilAc, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessosDAO.apagarRegistroFilial(nome, codfilAc, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void apagarCliente(PessoaCliAut cliente, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessosDAO.apagarRegistroCliente(cliente, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Apaga o registro da permissao
     *
     * @param nome Nome do usuario
     * @param sistema código do sistema
     * @param persistencia conexão com a base de dados
     * @throws Exception
     */
    public void apagarPermissoes(String nome, String sistema, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessosDAO.apagarRegistroPermissao(nome, sistema, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Bloquea o usuário
     *
     * @param nome nome do usuário
     * @param persistencia conexão com base de dados
     * @throws Exception
     */
    public void bloquearUsuario(String nome, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessosDAO.bloquearUsuario(nome, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista todos os grupos da empresa
     *
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<SASGrupos> listarGrupos(Persistencia persistencia) throws Exception {
        try {
            SASGruposDao sasgruposdao = new SASGruposDao();
            return sasgruposdao.listaSASGrupos(persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void adiconarGrupos(SASGrupos grupo, Persistencia persistencia) throws Exception {
        try {
            SASGruposDao sasgruposdao = new SASGruposDao();
            sasgruposdao.inserirGrupo(grupo, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void editarGrupos(SASGrupos grupo, Persistencia persistencia) throws Exception {
        try {
            SASGruposDao sasgruposdao = new SASGruposDao();
            sasgruposdao.editarGrupo(grupo, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista o grupo em que a pessoa pertence
     *
     * @param grupo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<SASGrupos> listarGrupos(String grupo, Persistencia persistencia) throws Exception {
        try {
            SASGruposDao sasgruposdao = new SASGruposDao();
            return sasgruposdao.listaSASGruposGrupo(grupo, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<PessoaLogin> listarPessoaLogin(BigDecimal codigo, Persistencia persistencia) throws Exception {
        try {
            PessoaLoginDao pessoaLoginDao = new PessoaLoginDao();
            return pessoaLoginDao.getPessoaLogin(codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<SasPWFill> listarFiliaisPermissao(String sNome, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            List<SasPWFill> filiais;
            filiais = saspwfildao.getSasPWFill(sNome, persistencia);
            return filiais;
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<PessoaPortalSrv> listarServicosUsuario(BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            PessoaPortalSrvDao pessoaPortalSrvDao = new PessoaPortalSrvDao();
            return pessoaPortalSrvDao.listarServicosUsuario(codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void apagarServicoUsuario(PessoaPortalSrv pessoaPortalSrv, Persistencia persistencia) throws Exception {
        try {
            PessoaPortalSrvDao pessoaPortalSrvDao = new PessoaPortalSrvDao();
            pessoaPortalSrvDao.removerServico(pessoaPortalSrv, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<PortalSrv> listarServicos(Persistencia persistencia) throws Exception {
        try {
            PortalSrvDao portalSrvDao = new PortalSrvDao();
            return portalSrvDao.listarServicos(persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<SaspwacSysdef> listarPermissoesUsuario(String nome, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            List<SaspwacSysdef> permissoes;
            permissoes = acessosdao.listaPermissoes(nome, persistencia);
            return permissoes;
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<SasPWFill> listarTodasFiliais(Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            List<SasPWFill> filiais;
            filiais = acessosdao.listaTodasFiliais(persistencia);
            return filiais;
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("ClientesSatMobWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Pessoa> listaPessoaQuery(String query, Persistencia persistencia) throws Exception {
        List<Pessoa> retorno = new ArrayList<>();
        try {
            PessoaDao pessoaDao = new PessoaDao();
            retorno = pessoaDao.listagemPessoaQuery(query, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
        return retorno;
    }

    public Pessoa inserirCodPessoaWeb(Pessoa pessoa, Persistencia local, Persistencia central) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            if (local.getEmpresa().equals(central.getEmpresa())) {
                pessoa.setCodPessoaWEB(pessoa.getCodigo());
                pessoaDao.updateCodPessoaWeb(pessoa, central);
            } else {
                pessoa.setCodPessoaWEB(pessoaDao.inserirPessoaExpressaCentral(pessoa, central));
                pessoaDao.updateCodPessoaWeb(pessoa, local);
            }
            return pessoa;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public SasPWFill buscarFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            return saspwfildao.buscaSasPWFill(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<Sysdef> listarTodasPermissoes(Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            List<Sysdef> permissoes;
            permissoes = acessosdao.listaTodasPermissoes(persistencia);
            return permissoes;
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<Sysdef> listarPermissoesGrupo(BigDecimal codGrupo, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            List<Sysdef> permissoes;
            permissoes = acessosdao.listaPermissoesGrupo(codGrupo, persistencia);
            return permissoes;
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<PessoaCliAut> listarClientes(BigDecimal codPessoa, Boolean exclusao, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.listaClientes(codPessoa, exclusao, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista todos os usuários por cliente liberado.
     *
     * @param filtros
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Saspw> listarUsuariosClientes(Map filtros, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.listarUsuariosCliente(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista todos os usuários por cliente liberado.
     *
     * @param filtros
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Saspw> listarUsuariosGrupo(Map filtros, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.listarUsuariosGrupo(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista todos os clientes liberados em um grupo.
     *
     * @param codGrupo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Clientes> listarClientesGrupo(String codGrupo, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesDao = new ClientesDao();
            return clientesDao.listaClientesGrupo(codGrupo, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> buscarClientes(String nome, String query, Persistencia persistencia)
            throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.BuscaClientes(nome, query, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> buscarClientes(String query, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.buscaClientes(query, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    public List<PessoaCliAut> buscarClientesPermissao(List<SasPWFill> filiais, String query, String codigo, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.buscaClientesPermissaoServico(filiais, query, codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("acessossatmobweb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> buscarClientes(List<SasPWFill> filiais, String query, Persistencia persistencia)
            throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.buscaClientes(filiais, query, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> buscarClientes(List<SasPWFill> filiais, String codPessoa, String query, Persistencia persistencia)
            throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.buscaClientes(filiais, codPessoa, query, persistencia);
        } catch (Exception e) {
            throw new Exception("acessossatmobweb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> buscarClientes(BigDecimal codpessoa, String query, Persistencia persistencia)
            throws Exception {
        try {
            ClientesDao acessosdao = new ClientesDao();
            return acessosdao.QueryCliente(codpessoa, query, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca a pessoa pelo CodPessoaWeb
     *
     * @param codpesWeb CodPessoaWeb
     * @param persistencia Conexão com o banco
     * @return Retorna pessoa de SASPW
     */
    public Saspw BuscaLogin(BigDecimal codpesWeb, Persistencia persistencia) {
        Saspw retorno = new Saspw();
        SaspwDao saspwDao = new SaspwDao();
        try {
            retorno = saspwDao.BuscaLogin(codpesWeb, persistencia);
        } catch (Exception e) {
        }

        return retorno;
    }

    /* LISTAGENS PAGINADAS */
    /**
     * Contagem do cadastro de usuaarios
     *
     * @param filtros - filtros de pesquisa
     * @param codPessoa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            AcessosDao acessosdao = new AcessosDao();
            retorno = acessosdao.TotalUsuariosMobWeb(filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de usuarios
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo da pessoa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Saspw> listagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {

        try {
            List<Saspw> retorno;
            AcessosDao acessosdao = new AcessosDao();
            retorno = acessosdao.ListaPaginada(primeiro, linhas, filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de usuarios
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param persistencia conexão ao banco de dados
     * @param satellite
     * @return
     * @throws Exception
     */
    public List<UsuarioSatMobWeb> listagemUsuarios(int primeiro, int linhas, Map filtros, Persistencia persistencia, Persistencia satellite) throws Exception {
        try {
            List<UsuarioSatMobWeb> retorno;
            AcessosDao acessosdao = new AcessosDao();
            retorno = acessosdao.ListaUsuarios(primeiro, linhas, filtros, persistencia, satellite);
            return retorno;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public List<UsuarioSatMobWeb> listagemUsuarios(int primeiro, int linhas, Map filtros, String codPessoa,
            Persistencia persistencia, Persistencia satellite) throws Exception {
        try {
            List<UsuarioSatMobWeb> retorno;
            AcessosDao acessosdao = new AcessosDao();
            retorno = acessosdao.listaUsuariosServicos(primeiro, linhas, filtros, codPessoa, persistencia, satellite);
            return retorno;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca algum usuário vinculado ao código da pessoa
     *
     * @param codPessoaWeb
     * @param persistencia
     * @param satellite
     * @return
     * @throws Exception
     */
    public UsuarioSatMobWeb buscarUsuario(BigDecimal codPessoaWeb, Persistencia persistencia, Persistencia satellite) throws Exception {
        try {
            UsuarioSatMobWeb retorno;
            AcessosDao acessosdao = new AcessosDao();
            retorno = acessosdao.buscarUsuario(codPessoaWeb, persistencia, satellite);
            return retorno;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagemAcessos(Map filtros, Persistencia persistencia, Persistencia satellite) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.TotalAcessosMobWeb(filtros, persistencia, satellite);
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagemAcessos(Map filtros, String codPessoa, Persistencia persistencia, Persistencia satellite) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.totalAcessosMobWeb(filtros, codPessoa, persistencia, satellite);
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Atualiza a lista de grupos com os bancos ausentes
     *
     * @param persistencia
     * @throws Exception
     */
    public void atualizarGrupos(Persistencia persistencia) throws Exception {
        try {
            SASGruposDao sasGruposDao = new SASGruposDao();
            sasGruposDao.atualizarListaGrupos(persistencia);
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public Saspw selecionarUsuario(BigDecimal codPessoa, Persistencia local) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.getUsuario(codPessoa, local);
        } catch (Exception e) {
            throw new Exception("login.falhaselecionarusuario<message>" + e.getMessage());
        }
    }

    /**
     * Busca SASPW para algum codPessoaWeb já cadastrado.
     *
     * @param codPessoaWeb
     * @param local
     * @return
     * @throws Exception
     */
    public Saspw selecionarUsuarioWeb(BigDecimal codPessoaWeb, Persistencia local) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.getUsuarioWeb(codPessoaWeb, local);
        } catch (Exception e) {
            throw new Exception("login.falhaselecionarusuario<message>" + e.getMessage());
        }
    }
}
