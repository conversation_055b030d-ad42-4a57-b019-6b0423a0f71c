/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.controller.guias;

import Dados.Persistencia;
import SasBeans.CxFGuiasVol;
import SasBeans.Filiais;
import SasBeans.Rt_Guias;
import SasBeansCompostas.EGtv;
import SasDaos.CxFGuiasVolDao;
import SasDaos.EGtvDao;
import SasDaos.FiliaisDao;
import SasDaos.Rt_GuiasDao;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class GuiasSPM {

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("GuiasSPM.falhageral<message>" + e.getMessage());
        }
    }

    public List<EGtv> listaPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            EGtvDao guiasClienteDAO = new EGtvDao();
            return guiasClienteDAO.listaPaginadaSPM(primeiro, linhas, filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("GuiasSPM.falhageral<message>" + e.getMessage());
        }
    }

    public List<EGtv> listaGuias(String seqRota, String parada, String data, Persistencia persistencia) throws Exception {
        try {
            EGtvDao guiasClienteDAO = new EGtvDao();
            return guiasClienteDAO.listaSPM(seqRota, parada, data, persistencia);
        } catch (Exception e) {
            throw new Exception("GuiasSPM.falhageral<message>" + e.getMessage());
        }
    }

    public Integer totalGuias(Map filtros, Persistencia persistencia) throws Exception {
        try {
            EGtvDao guiasClienteDAO = new EGtvDao();
            return guiasClienteDAO.qtdTotalGuiasSPM(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("GuiasSPM.falhageral<message>" + e.getMessage());
        }
    }

    public Rt_Guias obterInfoGuia(EGtv eGTV, Persistencia persistencia) throws Exception {
        try {
            Rt_GuiasDao rt_guiasDao = new Rt_GuiasDao();
            if (eGTV.getOperacao().equals("E") || eGTV.getOperacao().contains("Entrega")) {
                return rt_guiasDao.infoGuiaEntrega(eGTV.getGuia(), eGTV.getSerie(), persistencia);
            } else {
                return rt_guiasDao.infoGuia(eGTV.getGuia(), eGTV.getSerie(), null != eGTV.getPedido() && !eGTV.getPedido().equals(""), persistencia);
            }
        } catch (Exception e) {
            throw new Exception("GuiasSPM.falhageral<message>" + e.getMessage());
        }
    }

    public List<CxFGuiasVol> listarLacres(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            CxFGuiasVolDao cxfguiasvoldao = new CxFGuiasVolDao();
            return cxfguiasvoldao.getLacres(guia, serie, persistencia);
        } catch (Exception e) {
            throw new Exception("GuiasSPM.falhageral<message>" + e.getMessage());
        }
    }
}
