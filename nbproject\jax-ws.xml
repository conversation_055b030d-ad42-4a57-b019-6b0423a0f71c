<?xml version="1.0" encoding="UTF-8"?>
<jax-ws xmlns="http://www.netbeans.org/ns/jax-ws/1">
  <services/>
  <clients>
    <client name="nfse-goiania-wsdl">
      <wsdl-url>file:/C:/Users/<USER>/Desktop/nfse-goiania-wsdl.asmx</wsdl-url>
      <local-wsdl-file>nfse-goiania-wsdl.asmx.wsdl</local-wsdl-file>
      <package-name>br.gov.go.goiania.nfse.ws</package-name>
      <catalog-file>catalog.xml</catalog-file>
      <wsimport-options>
        <wsimport-option>
          <wsimport-option-name>extension</wsimport-option-name>
          <wsimport-option-value>true</wsimport-option-value>
        </wsimport-option>
        <wsimport-option>
          <wsimport-option-name>verbose</wsimport-option-name>
          <wsimport-option-value>true</wsimport-option-value>
        </wsimport-option>
        <wsimport-option>
          <wsimport-option-name>fork</wsimport-option-name>
          <wsimport-option-value>false</wsimport-option-value>
        </wsimport-option>
        <wsimport-option>
          <wsimport-option-name>wsdlLocation</wsimport-option-name>
          <wsimport-option-value>file:/C:/Users/<USER>/Desktop/nfse-goiania-wsdl.asmx</wsimport-option-value>
        </wsimport-option>
        <wsimport-option>
          <wsimport-option-name>xnocompile</wsimport-option-name>
          <wsimport-option-value>true</wsimport-option-value>
        </wsimport-option>
        <wsimport-option>
          <wsimport-option-name>xendorsed</wsimport-option-name>
          <wsimport-option-value>true</wsimport-option-value>
        </wsimport-option>
      </wsimport-options>
    </client>
  </clients>
</jax-ws>
