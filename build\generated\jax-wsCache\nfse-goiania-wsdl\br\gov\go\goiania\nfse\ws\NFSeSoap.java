
package br.gov.go.goiania.nfse.ws;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.3.0
 * Generated source version: 2.2
 * 
 */
@WebService(name = "NFSeSoap", targetNamespace = "http://nfse.goiania.go.gov.br/ws/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface NFSeSoap {


    /**
     * 
     * @param arquivoXML
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "GerarNfse", action = "http://nfse.goiania.go.gov.br/ws/GerarNfse")
    @WebResult(name = "GerarNfseResult", targetNamespace = "http://nfse.goiania.go.gov.br/ws/")
    @RequestWrapper(localName = "GerarNfse", targetNamespace = "http://nfse.goiania.go.gov.br/ws/", className = "br.gov.go.goiania.nfse.ws.GerarNfse")
    @ResponseWrapper(localName = "GerarNfseResponse", targetNamespace = "http://nfse.goiania.go.gov.br/ws/", className = "br.gov.go.goiania.nfse.ws.GerarNfseResponse")
    public String gerarNfse(
        @WebParam(name = "ArquivoXML", targetNamespace = "http://nfse.goiania.go.gov.br/ws/")
        String arquivoXML);

    /**
     * 
     * @param arquivoXML
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "ConsultarNfseRps", action = "http://nfse.goiania.go.gov.br/ws/ConsultarNfseRps")
    @WebResult(name = "ConsultarNfseRpsResult", targetNamespace = "http://nfse.goiania.go.gov.br/ws/")
    @RequestWrapper(localName = "ConsultarNfseRps", targetNamespace = "http://nfse.goiania.go.gov.br/ws/", className = "br.gov.go.goiania.nfse.ws.ConsultarNfseRps")
    @ResponseWrapper(localName = "ConsultarNfseRpsResponse", targetNamespace = "http://nfse.goiania.go.gov.br/ws/", className = "br.gov.go.goiania.nfse.ws.ConsultarNfseRpsResponse")
    public String consultarNfseRps(
        @WebParam(name = "ArquivoXML", targetNamespace = "http://nfse.goiania.go.gov.br/ws/")
        String arquivoXML);

}
