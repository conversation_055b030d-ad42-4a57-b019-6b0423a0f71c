package webservice_nfse_goiania;

/**
 *
 * <AUTHOR>
 */
public class NFSE_Goiania {
    
    public String Gerar(String xml){
        //System.setProperty("https.protocols", "SSLv3");
        return this.gerarNfse(xml);
    }
    
    public String Consultar(String xml){
        //System.setProperty("https.protocols", "SSLv3");
        return this.consultarNfseRps(xml);
    }

    private String gerarNfse(java.lang.String arquivoXML) {
        br.gov.go.goiania.nfse.ws.NFSe service = new br.gov.go.goiania.nfse.ws.NFSe();
        br.gov.go.goiania.nfse.ws.NFSeSoap port = service.getNFSeSoap12();
        return port.gerarNfse(arquivoXML);
    }

    private String consultarNfseRps(java.lang.String arquivoXML) {
        br.gov.go.goiania.nfse.ws.NFSe service = new br.gov.go.goiania.nfse.ws.NFSe();
        br.gov.go.goiania.nfse.ws.NFSeSoap port = service.getNFSeSoap12();
        return port.consultarNfseRps(arquivoXML);
    }
}
