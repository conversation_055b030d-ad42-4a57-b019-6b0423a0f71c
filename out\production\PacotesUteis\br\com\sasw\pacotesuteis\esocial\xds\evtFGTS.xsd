﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtFGTS/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtFGTS/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtFGTS">
                    <xs:annotation>
                        <xs:documentation>Evento FGTS consolidado</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento">
                                <xs:annotation>
                                    <xs:documentation>Identificacao do evento de retorno</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="perApur">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Periodo ao qual se referem as informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="infoFGTS">
                                <xs:annotation>
                                    <xs:documentation>Informacoes relativas ao FGTS</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="nrRecArqBase">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Recibo do arquivo de origem</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="1"/>
                                                    <xs:maxLength value="40"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="indExistInfo">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Existe Informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="infoBaseFGTS" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Bases de calculo do FGTS</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="basePerApur" minOccurs="0" maxOccurs="21">
                                                        <xs:annotation>
                                                            <xs:documentation>Bases de calculo do FGTS, exceto se {tpAcConv} = [E]</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="tpValor">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Tipo de valor</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d{1,2}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="baseFGTS">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Remuneracao (valor da base de calculo) do FGTS</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="infoBasePerAntE" minOccurs="0" maxOccurs="180">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes sobre bases de calculo do FGTS, quando {tpAcConv} = [E]</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="perRef">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Periodo de referencia</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="basePerAntE" maxOccurs="11">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Bases de calculo do FGTS, quando {tpAcConv} = [E]</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="tpValorE">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Tipo de valor que influi na apuracao do FGTS</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:byte">
                                                                                        <xs:pattern value="\d{2}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="baseFGTSE">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Remuneracao (valor da base de calculo) do FGTS</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="infoDpsFGTS" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes sobre FGTS agrupadas por tipo de depOsito</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="dpsPerApur" minOccurs="0" maxOccurs="20">
                                                        <xs:annotation>
                                                            <xs:documentation>Valores de FGTS, exceto se {tpAcConv} = [E]</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="tpDps">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Tipo de depOsito</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d{2}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vrFGTS">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor FGTS</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="infoDpsPerAntE" minOccurs="0" maxOccurs="180">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes sobre o FGTS quando {tpAcConv} = [E]</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="perRef">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Periodo de referencia</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dpsPerAntE" maxOccurs="10">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Valores de FGTS quando {tpAcConv} = [E]</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="tpDpsE">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Tipo de depOsito</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:byte">
                                                                                        <xs:pattern value="\d{2}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="vrFGTSE">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Valor histOrico do FGTS</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:decimal">
                                                                                        <xs:totalDigits value="14"/>
                                                                                        <xs:fractionDigits value="2"/>
                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
