package com.br.sasw.esocial_novo.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;


@Entity
@Table(name = "EmailsEnviar")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmailsEnviar {

    @Id
    @Column(name = "Sequencia")
    private BigDecimal sequencia;

    @Column(name = "SMTP", length = 100)
    private String smtp;

    @Column(name = "Dest_Email", length = 500)
    private String destEmail;

    @Column(name = "Dest_Nome", length = 200)
    private String destNome;

    @Column(name = "Remet_Email", length = 100)
    private String remetEmail;

    @Column(name = "Remet_Nome", length = 100)
    private String remetNome;

    @Column(name = "Assunto", length = 200)
    private String assunto;

    @Column(name = "Mensagem", columnDefinition = "TEXT")
    private String mensagem;

    @Column(name = "Aut_Login", length = 100)
    private String autLogin;

    @Column(name = "Aut_Senha", length = 100)
    private String autSenha;

    @Column(name = "Porta")
    private Integer porta;

    @Column(name = "Flag_Enviado", length = 1)
    private String flagEnviado;

    @Column(name = "DT_Inclusao")
    private LocalDate dtInclusao;

    @Column(name = "Hr_Inclusao")
    private String hrInclusao;

    @Column(name = "Dt_Envio")
    private LocalDate dtEnvio;

    @Column(name = "Hr_Envio")
    private String hrEnvio;

    @Column(name = "codFil", length = 10)
    private String codFil;

    @Column(name = "codCli", length = 10)
    private String codCli;

    @Column(name = "codContato", length = 10)
    private String codContato;

    @Column(name = "parametro", length = 50)
    private String parametro;

    @OneToMany(mappedBy = "emailsEnviar", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<EmailsEnviarAnexo> anexos;

    public boolean isEnviado() {
        return "*".equals(flagEnviado) || "e".equals(flagEnviado);
    }

    public void marcarComoEnviado() {
        this.flagEnviado = "*";
        this.dtEnvio = LocalDate.now();
        this.hrEnvio = LocalTime.now().format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss"));
    }

    public void marcarComoFalha() {
        this.flagEnviado = "e";
    }

    public String[] getDestinatariosLimpos() {
        if (destEmail == null || destEmail.trim().isEmpty()) {
            return new String[0];
        }
        
        String emailLimpo = destEmail
            .replace("; ", ";")
            .replace(" ;", ";")
            .replace(",", ";")
            .replace(" ", "");
            
        return emailLimpo.split(";");
    }
}
