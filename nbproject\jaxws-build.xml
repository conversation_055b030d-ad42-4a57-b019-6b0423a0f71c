<?xml version="1.0" encoding="UTF-8"?><!--
        *** GENERATED FROM jax-ws.xml - DO NOT EDIT !                             ***
        *** TO MODIFY wsimport options USE Web Service node -> Edit WS Attributes ***
        *** TO CHANGE TARGETS GENERATED TO jaxws-build.xml COPY THOSE             ***
        *** TARGETS TO ../build.xml AND MODIFY THAT FILE INSTEAD                  ***

        --><project xmlns:xalan="http://xml.apache.org/xslt" xmlns:j2seproject3="http://www.netbeans.org/ns/j2se-project/3" xmlns:jaxws="http://www.netbeans.org/ns/jax-ws/1">
    <!--
                ===================
                JAX-WS WSIMPORT SECTION
                ===================
            -->
    <property name="BUILD_LIBS" location="../Bibliotecas/" />
    <target name="wsimport-init" depends="init">
        <mkdir dir="${build.generated.sources.dir}/jax-ws"/>
        <taskdef name="wsimport" classname="com.sun.tools.ws.ant.WsImport">
            <classpath location="${BUILD_LIBS}/jaxws-ri-2.3.0/jaxws-ri/lib/jaxws-tools.jar"/>
        </taskdef>
    </target>
    <target name="wsimport-client-nfse-goiania-wsdl" depends="wsimport-init">
        <mkdir dir="${build.generated.dir}/jax-wsCache/nfse-goiania-wsdl"/>
        <property name="wsdl-nfse-goiania-wsdl" location="xml-resources/web-service-references/nfse-goiania-wsdl/wsdl/nfse-goiania-wsdl.asmx.wsdl"/>
        <wsimport sourcedestdir="${build.generated.dir}/jax-wsCache/nfse-goiania-wsdl" destdir="${build.generated.dir}/jax-wsCache/nfse-goiania-wsdl" wsdl="${wsdl-nfse-goiania-wsdl}" catalog="catalog.xml" extension="true" verbose="true" fork="false" wsdlLocation="file:/C:/Users/<USER>/Desktop/nfse-goiania-wsdl.asmx" xnocompile="true" xendorsed="true">
            <depends file="${wsdl-nfse-goiania-wsdl}"/>
            <produces dir="${build.generated.dir}/jax-wsCache/nfse-goiania-wsdl"/>
        </wsimport>
        <copy todir="${build.generated.sources.dir}/jax-ws">
            <fileset dir="${build.generated.dir}/jax-wsCache/nfse-goiania-wsdl">
                <include name="**/*.java"/>
            </fileset>
        </copy>
    </target>
    <target name="wsimport-client-clean-nfse-goiania-wsdl" depends="-init-project">
        <delete dir="${build.generated.dir}/jax-wsCache/nfse-goiania-wsdl"/>
        <delete dir="${build.generated.sources.dir}/jax-ws/br/gov/go/goiania/nfse/ws"/>
    </target>
    <target name="wsimport-client-generate" depends="wsimport-client-nfse-goiania-wsdl"/>
</project>
