<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://nfse.goiania.go.gov.br/ws/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" targetNamespace="http://nfse.goiania.go.gov.br/ws/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://nfse.goiania.go.gov.br/ws/">
      <s:element name="GerarNfse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ArquivoXML" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GerarNfseResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GerarNfseResult" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ConsultarNfseRps">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ArquivoXML" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ConsultarNfseRpsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ConsultarNfseRpsResult" type="s:string"/>
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="GerarNfseSoapIn">
    <wsdl:part name="parameters" element="tns:GerarNfse"/>
  </wsdl:message>
  <wsdl:message name="GerarNfseSoapOut">
    <wsdl:part name="parameters" element="tns:GerarNfseResponse"/>
  </wsdl:message>
  <wsdl:message name="ConsultarNfseRpsSoapIn">
    <wsdl:part name="parameters" element="tns:ConsultarNfseRps"/>
  </wsdl:message>
  <wsdl:message name="ConsultarNfseRpsSoapOut">
    <wsdl:part name="parameters" element="tns:ConsultarNfseRpsResponse"/>
  </wsdl:message>
  <wsdl:portType name="NFSeSoap">
    <wsdl:operation name="GerarNfse">
      <wsdl:input message="tns:GerarNfseSoapIn"/>
      <wsdl:output message="tns:GerarNfseSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ConsultarNfseRps">
      <wsdl:input message="tns:ConsultarNfseRpsSoapIn"/>
      <wsdl:output message="tns:ConsultarNfseRpsSoapOut"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="NFSeSoap" type="tns:NFSeSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="GerarNfse">
      <soap:operation soapAction="http://nfse.goiania.go.gov.br/ws/GerarNfse" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ConsultarNfseRps">
      <soap:operation soapAction="http://nfse.goiania.go.gov.br/ws/ConsultarNfseRps" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="NFSeSoap12" type="tns:NFSeSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="GerarNfse">
      <soap12:operation soapAction="http://nfse.goiania.go.gov.br/ws/GerarNfse" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ConsultarNfseRps">
      <soap12:operation soapAction="http://nfse.goiania.go.gov.br/ws/ConsultarNfseRps" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="NFSe">
    <wsdl:port name="NFSeSoap" binding="tns:NFSeSoap">
      <soap:address location="https://nfse.goiania.go.gov.br/ws/nfse.asmx"/>
    </wsdl:port>
    <wsdl:port name="NFSeSoap12" binding="tns:NFSeSoap12">
      <soap12:address location="https://nfse.goiania.go.gov.br/ws/nfse.asmx"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>