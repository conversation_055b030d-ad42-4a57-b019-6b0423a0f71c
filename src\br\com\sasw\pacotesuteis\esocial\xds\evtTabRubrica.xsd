﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtTabRubrica/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtTabRubrica/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtTabRubrica">
                    <xs:annotation>
                        <xs:documentation>Evento Tabela de Rubricas</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeCadastro">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="infoRubrica">
                                <xs:annotation>
                                    <xs:documentation>Informacoes da Rubrica</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:choice>
                                            <xs:element name="inclusao">
                                                <xs:annotation>
                                                    <xs:documentation>Inclusao de novas informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideRubrica" type="TIdeRubrica">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes de identificacao da rubrica e validade das informacoes</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="dadosRubrica" type="TDadosRubrica">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes da rubrica</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="alteracao">
                                                <xs:annotation>
                                                    <xs:documentation>Alteracao das informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideRubrica" type="TIdeRubrica">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes de identificacao da rubrica</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="dadosRubrica" type="TDadosRubrica">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes da rubrica</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="novaValidade" type="TPeriodoValidade" minOccurs="0">
                                                            <xs:annotation>
                                                                <xs:documentation>Novo periodo de validade das informacoes</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="exclusao">
                                                <xs:annotation>
                                                    <xs:documentation>Exclusao das informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideRubrica" type="TIdeRubrica">
                                                            <xs:annotation>
                                                                <xs:documentation>Identificacao da rubrica que sera excluida</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                        </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeCadastro">
        <xs:annotation>
            <xs:documentation>Identificacao de evento de cadastro/tabelas</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TIdeRubrica">
        <xs:annotation>
            <xs:documentation>Identificacao da Rubrica e periodo de validade</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="codRubr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo da Rubrica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="30"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="ideTabRubr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Identificador da tabela de rubricas</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="8"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="iniValid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Inicio da validade (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fimValid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo final da validade, no formato (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TDadosRubrica">
        <xs:annotation>
            <xs:documentation>Informacoes da Rubrica</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="dscRubr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Descricao (nome) da rubrica no sistema de folha de pagamento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="100"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="natRubr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo de Classificacao da Rubrica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:integer">
                        <xs:pattern value="\d{4}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpRubr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de rubrica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="codIncCP">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Incidencia para a Previdencia Social</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:length value="2"/>
                        <xs:pattern value="\d{2}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="codIncIRRF">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Incidencia para o IRRF</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:length value="2"/>
                        <xs:pattern value="\d{2}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="codIncFGTS">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Incidencia para o FGTS</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:length value="2"/>
                        <xs:pattern value="\d{2}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="codIncSIND">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Incidencia para a Contribuicao Sindical</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:length value="2"/>
                        <xs:pattern value="\d{2}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="observacao" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Observacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="255"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="ideProcessoCP" minOccurs="0" maxOccurs="99">
                <xs:annotation>
                    <xs:documentation>Identificacao de Processo - Incidencia de Contrib. Previdenciaria</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="tpProc">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Tipo de Processo</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:byte">
                                    <xs:pattern value="\d"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nrProc">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Número do Processo</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="21"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="extDecisao">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Extensao da Decisao/Sentenca</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:byte">
                                    <xs:pattern value="\d"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="codSusp">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>COdigo do Indicativo da Suspensao</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:integer">
                                    <xs:pattern value="\d{1,14}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="ideProcessoIRRF" minOccurs="0" maxOccurs="99">
                <xs:annotation>
                    <xs:documentation>Identificacao de Processo - Incidencia de IRRF</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="nrProc">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Número do Processo</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="21"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="codSusp">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>COdigo do Indicativo da Suspensao</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:integer">
                                    <xs:pattern value="\d{1,14}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="ideProcessoFGTS" minOccurs="0" maxOccurs="99">
                <xs:annotation>
                    <xs:documentation>Identificacao de Processo - Incidencia de FGTS</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="nrProc">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Número do Processo</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="21"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="ideProcessoSIND" minOccurs="0" maxOccurs="99">
                <xs:annotation>
                    <xs:documentation>Identificacao de Processo - Incidencia de Contrib. Sindical</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="nrProc">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Número do Processo</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:minLength value="2"/>
                                    <xs:maxLength value="21"/>
                                    <xs:whiteSpace value="preserve"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TPeriodoValidade">
        <xs:annotation>
            <xs:documentation>Periodo de validade das informacoes</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="iniValid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Inicio da validade (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fimValid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo final da validade, no formato (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
