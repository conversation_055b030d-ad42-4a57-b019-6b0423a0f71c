annotation.processing.enabled=true
annotation.processing.enabled.in.editor=false
annotation.processing.processors.list=
annotation.processing.run.all.processors=true
annotation.processing.source.output=${build.generated.sources.dir}/ap-source-output
application.title=PacotesUteis
application.vendor=Des-Erico
build.classes.dir=${build.dir}/classes
build.classes.excludes=**/*.java,**/*.form
# This directory is removed when the project is cleaned:
build.dir=build
build.generated.dir=${build.dir}/generated
build.generated.sources.dir=${build.dir}/generated-sources
# Only compile against the classpath explicitly listed here:
build.sysclasspath=ignore
build.test.classes.dir=${build.dir}/test/classes
build.test.results.dir=${build.dir}/test/results
# Uncomment to specify the preferred debugger connection transport:
#debug.transport=dt_socket
debug.classpath=\
    ${run.classpath}
debug.modulepath=\
    ${run.modulepath}
debug.test.classpath=\
    ${run.test.classpath}
debug.test.modulepath=\
    ${run.test.modulepath}
# This directory is removed when the project is cleaned:
dist.dir=dist
dist.jar=${dist.dir}/PacotesUteis.jar
dist.javadoc.dir=${dist.dir}/javadoc
endorsed.classpath=\
    ${libs.JAXB-ENDORSED.classpath}
excludes=
file.reference.axiom-api-1.2.12.jar=..\\Bibliotecas\\br.com.WebServiceEsocial\\dist\\lib\\axiom-api-1.2.12.jar
file.reference.axiom-dom-1.2.12.jar=..\\Bibliotecas\\br.com.WebServiceEsocial\\dist\\lib\\axiom-dom-1.2.12.jar
file.reference.axiom-impl-1.2.12.jar=..\\Bibliotecas\\br.com.WebServiceEsocial\\dist\\lib\\axiom-impl-1.2.12.jar
file.reference.axis2-adb-1.6.1.jar=..\\Bibliotecas\\br.com.WebServiceEsocial\\dist\\lib\\axis2-adb-1.6.1.jar
file.reference.bcprov-jdk16-1.46.jar=..\\Bibliotecas\\bcprov-jdk16-1.46.jar
file.reference.com.lowagie.text-2.1.7.jar=..\\Bibliotecas\\ConvertHTMLPDF\\com.lowagie.text-2.1.7.jar
file.reference.commons-codec-1.11.jar=..\\Bibliotecas\\httpclient\\commons-codec-1.11.jar
file.reference.commons-email-1.3.1.jar=..\\Bibliotecas\\commons-email-1.3.1\\commons-email-1.3.1.jar
file.reference.commons-logging-1.2.jar=..\\Bibliotecas\\httpclient\\commons-logging-1.2.jar
file.reference.flying-saucer-core-9.1.7.jar=..\\Bibliotecas\\ConvertHTMLPDF\\flying-saucer-core-9.1.7.jar
file.reference.flying-saucer-pdf-9.1.7.jar=..\\Bibliotecas\\ConvertHTMLPDF\\flying-saucer-pdf-9.1.7.jar
file.reference.flying-saucer-pdf-itext5-9.1.7.jar=..\\Bibliotecas\\ConvertHTMLPDF\\flying-saucer-pdf-itext5-9.1.7.jar
file.reference.httpclient-4.5.12.jar=..\\Bibliotecas\\httpclient\\httpclient-4.5.12.jar
file.reference.httpcore-4.4.13.jar=..\\Bibliotecas\\httpclient\\httpcore-4.4.13.jar
file.reference.java_certificado-2.3.jar=..\\Bibliotecas\\java_certificado-2.3.jar
file.reference.javax.servlet-api-3.0.1.jar=..\\Bibliotecas\\javax.servlet-api-3.0.1.jar
file.reference.jcommon-1.0.23.jar=..\\Bibliotecas\\jfreechart\\jcommon-1.0.23\\jcommon-1.0.23.jar
file.reference.jfreechart-1.0.19.jar=..\\Bibliotecas\\jfreechart\\jfreechart-1.0.19\\lib\\jfreechart-1.0.19.jar
file.reference.jmockit-1.24.jar=..\\Bibliotecas\\jmockit-1.24.jar
file.reference.jtidy-r938.jar=..\\Bibliotecas\\ConvertHTMLPDF\\jtidy-r938.jar
file.reference.junit-jupiter-engine-5.3.1.jar=..\\Bibliotecas\\junit-jupiter-engine-5.3.1.jar
file.reference.sqljdbc4.jar=..\\Bibliotecas\\sqljdbc_4.0.2206.100_ptb\\sqljdbc_4.0\\ptb\\sqljdbc4.jar
file.reference.xhtmlrenderer-8.7-atlassian-2.jar=..\\Bibliotecas\\ConvertHTMLPDF\\xhtmlrenderer-8.7-atlassian-2.jar
file.reference.xstream-1.4.20.jar=..\\Bibliotecas\\xstream-1.4.20\\lib\\xstream-1.4.20.jar
includes=**
jar.archive.disabled=${jnlp.enabled}
jar.compress=false
jar.index=${jnlp.enabled}
javac.classpath=\
    ${file.reference.javax.servlet-api-3.0.1.jar}:\
    ${file.reference.commons-email-1.3.1.jar}:\
    ${file.reference.jcommon-1.0.23.jar}:\
    ${file.reference.sqljdbc4.jar}:\
    ${file.reference.jfreechart-1.0.19.jar}:\
    ${file.reference.com.lowagie.text-2.1.7.jar}:\
    ${file.reference.flying-saucer-core-9.1.7.jar}:\
    ${file.reference.flying-saucer-pdf-9.1.7.jar}:\
    ${file.reference.flying-saucer-pdf-itext5-9.1.7.jar}:\
    ${file.reference.jtidy-r938.jar}:\
    ${file.reference.xhtmlrenderer-8.7-atlassian-2.jar}:\
    ${file.reference.axis2-adb-1.6.1.jar}:\
    ${file.reference.axiom-api-1.2.12.jar}:\
    ${file.reference.axiom-dom-1.2.12.jar}:\
    ${file.reference.axiom-impl-1.2.12.jar}:\
    ${file.reference.httpclient-4.5.12.jar}:\
    ${file.reference.commons-codec-1.11.jar}:\
    ${file.reference.commons-logging-1.2.jar}:\
    ${file.reference.java_certificado-2.3.jar}:\
    ${file.reference.bcprov-jdk16-1.46.jar}:\
    ${file.reference.jmockit-1.24.jar}:\
    ${file.reference.junit-jupiter-engine-5.3.1.jar}:\
    ${file.reference.httpcore-4.4.13.jar}:\
    ${file.reference.xstream-1.4.20.jar}
# Space-separated list of extra javac options
javac.compilerargs=
javac.deprecation=false
javac.external.vm=false
javac.modulepath=
javac.processormodulepath=
javac.processorpath=\
    ${javac.classpath}
javac.source=1.8
javac.target=1.8
javac.test.classpath=\
    ${javac.classpath}:\
    ${build.classes.dir}
javac.test.modulepath=\
    ${javac.modulepath}
javac.test.processorpath=\
    ${javac.test.classpath}
javadoc.additionalparam=
javadoc.author=false
javadoc.encoding=${source.encoding}
javadoc.html5=false
javadoc.noindex=false
javadoc.nonavbar=false
javadoc.notree=false
javadoc.private=false
javadoc.splitindex=true
javadoc.use=true
javadoc.version=false
javadoc.windowtitle=
jaxbwiz.gensrc.classpath=${libs.jaxb.classpath}
jaxbwiz.xjcdef.classpath=${libs.jaxb.classpath}
jaxbwiz.xjcrun.classpath=${libs.jaxb.classpath}
jlink.launcher=false
jlink.launcher.name=PacotesUteis
jnlp.codebase.type=no.codebase
jnlp.descriptor=application
jnlp.enabled=false
jnlp.mixed.code=default
jnlp.offline-allowed=false
jnlp.signed=false
jnlp.signing=
jnlp.signing.alias=
jnlp.signing.keystore=
main.class=pacotesuteis.Main
# Optional override of default Application-Library-Allowable-Codebase attribute identifying the locations where your signed RIA is expected to be found.
manifest.custom.application.library.allowable.codebase=
# Optional override of default Caller-Allowable-Codebase attribute identifying the domains from which JavaScript code can make calls to your RIA without security prompts.
manifest.custom.caller.allowable.codebase=
# Optional override of default Codebase manifest attribute, use to prevent RIAs from being repurposed
manifest.custom.codebase=
# Optional override of default Permissions manifest attribute (supported values: sandbox, all-permissions)
manifest.custom.permissions=
manifest.file=manifest.mf
meta.inf.dir=${src.dir}/META-INF
mkdist.disabled=false
platform.active=default_platform
run.classpath=\
    ${javac.classpath}:\
    ${build.classes.dir}
# Space-separated list of JVM arguments used when running the project
# (you may also define separate properties like run-sys-prop.name=value instead of -Dname=value
# or test-sys-prop.name=value to set system properties for unit tests):
run.jvmargs=
run.modulepath=\
    ${javac.modulepath}
run.test.classpath=\
    ${javac.test.classpath}:\
    ${build.test.classes.dir}
run.test.modulepath=\
    ${javac.test.modulepath}
source.encoding=UTF-8
src.dir=src
