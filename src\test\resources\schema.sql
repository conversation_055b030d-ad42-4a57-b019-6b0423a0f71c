-- Schema para testes do sistema de emails
-- <PERSON><PERSON> as tabelas necessárias para os testes em H2

CREATE TABLE EmailsEnviar (
    Sequencia DECIMAL(18,0) PRIMARY KEY,
    SMTP VARCHAR(100),
    Dest_Email VARCHAR(500),
    Dest_Nome VARCHAR(200),
    Remet_Email VARCHAR(100),
    Remet_Nome VARCHAR(100),
    Assunto VARCHAR(200),
    Mensagem TEXT,
    Aut_Login VARCHAR(100),
    Aut_Senha VARCHAR(100),
    Porta INTEGER,
    Flag_Enviado VARCHAR(1),
    DT_Inclusao DATE,
    Hr_Inclusao TIME,
    Dt_Envio DATE,
    Hr_Envio TIME,
    codFil VARCHAR(10),
    codCli VARCHAR(10),
    codContato VARCHAR(10),
    parametro VARCHAR(50)
);

CREATE TABLE EmailsEnviarAnexo (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    Sequencia DECIMAL(18,0),
    <PERSON><PERSON>m INTEGER,
    EndAnexo VARCHAR(500),
    DescAnexo VARCHAR(200),
    NomeAnexo VARCHAR(200),
    FOREIGN KEY (Sequencia) REFERENCES EmailsEnviar(Sequencia)
);

-- Dados de teste
INSERT INTO EmailsEnviar (
    Sequencia, SMTP, Dest_Email, Dest_Nome, Remet_Email, Remet_Nome,
    Assunto, Mensagem, Aut_Login, Aut_Senha, Porta, Flag_Enviado,
    DT_Inclusao, Hr_Inclusao, codFil, codCli, parametro
) VALUES (
    1, 'smtplw.com.br', '<EMAIL>', 'Teste 1', '<EMAIL>', 'SASw',
    'Email de Teste 1', '<html><body>Teste 1</body></html>', 'sasw', 'xNiadJEj9607', 587, '',
    CURRENT_DATE, CURRENT_TIME, '01', '001', 'TESTE'
);

INSERT INTO EmailsEnviar (
    Sequencia, SMTP, Dest_Email, Dest_Nome, Remet_Email, Remet_Nome,
    Assunto, Mensagem, Aut_Login, Aut_Senha, Porta, Flag_Enviado,
    DT_Inclusao, Hr_Inclusao, codFil, codCli, parametro
) VALUES (
    2, 'smtplw.com.br', '<EMAIL>;<EMAIL>', 'Teste 2', '<EMAIL>', 'SASw',
    'Email de Teste 2', 'Mensagem de teste simples', 'sasw', 'xNiadJEj9607', 587, '',
    CURRENT_DATE, CURRENT_TIME, '01', '002', 'SATGLOVAL'
);

INSERT INTO EmailsEnviar (
    Sequencia, SMTP, Dest_Email, Dest_Nome, Remet_Email, Remet_Nome,
    Assunto, Mensagem, Aut_Login, Aut_Senha, Porta, Flag_Enviado,
    DT_Inclusao, Hr_Inclusao, Dt_Envio, Hr_Envio, codFil, codCli, parametro
) VALUES (
    3, 'smtplw.com.br', '<EMAIL>', 'Teste 4', '<EMAIL>', 'SASw',
    'Email Já Enviado', 'Este email já foi enviado', 'sasw', 'xNiadJEj9607', 587, '*',
    CURRENT_DATE, CURRENT_TIME, CURRENT_DATE, CURRENT_TIME, '01', '003', 'TESTE'
);

INSERT INTO EmailsEnviarAnexo (Sequencia, Ordem, EndAnexo, DescAnexo, NomeAnexo)
VALUES (1, 1, 'teste.pdf', 'Arquivo de teste', 'teste.pdf');

INSERT INTO EmailsEnviarAnexo (Sequencia, Ordem, EndAnexo, DescAnexo, NomeAnexo)
VALUES (2, 1, 'documento.docx', 'Documento de teste', 'documento.docx');
