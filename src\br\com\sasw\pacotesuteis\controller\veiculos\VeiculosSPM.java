/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.controller.veiculos;

import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeans.SasPWFill;
import SasBeans.Veiculos;
import SasBeans.VeiculosMod;
import SasDaos.FiliaisDao;
import SasDaos.SasPwFilDao;
import SasDaos.VeiculosDao;
import br.com.sasw.pacotesuteis.sasdaos.VeiculosModDao;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class VeiculosSPM {

    public SasPWFill buscaFilial(String CodFil, BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            return saspwfildao.buscaSasPWFillLogin(CodFil, CodPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("VeiculosSPM.falhageral<message>" + e.getMessage());
        }
    }

    public void atualizarVeiculo(Veiculos veiculo, Persistencia persistencia) throws Exception {
        try {
            VeiculosDao veiculosDao = new VeiculosDao();
            veiculosDao.atualizarVeiculoSPM(veiculo, persistencia);
        } catch (Exception e) {
            throw new Exception("VeiculosSPM.falhageral<message>" + e.getMessage());
        }
    }

    public VeiculosMod buscarModelo(int codigo, Persistencia persistencia) throws Exception {
        try {
            VeiculosModDao veiculosModDao = new VeiculosModDao();
            return veiculosModDao.buscarModeloCodigo(codigo, persistencia);
        } catch (Exception e) {
            throw new Exception("VeiculosSPM.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca um modelo de veículo pela descrição exata do modelo.
     *
     * @param descricao
     * @param persistencia
     * @return
     * @throws Exception
     */
    public VeiculosMod buscarModeloDescricao(String descricao, Persistencia persistencia) throws Exception {
        try {
            VeiculosModDao veiculosModDao = new VeiculosModDao();
            return veiculosModDao.buscarModeloDescricao(descricao, persistencia);
        } catch (Exception e) {
            throw new Exception("VeiculosSPM.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirVeiculo(Veiculos veiculo, Persistencia persistencia) throws Exception {
        try {
            VeiculosDao veiculosDao = new VeiculosDao();
            veiculosDao.inserirVeiculoSPM(veiculo, persistencia);
        } catch (Exception e) {
            throw new Exception("VeiculosSPM.falhageral<message>" + e.getMessage());
        }
    }

    public boolean verificaVeiculoExiste(int numero, Persistencia persistencia) throws Exception {
        try {
            VeiculosDao veiculosDao = new VeiculosDao();
            Veiculos veiculo = veiculosDao.BuscaVeiculoNumero(numero, persistencia);

            return veiculo.getNumero() != 0;
        } catch (Exception e) {
            throw new Exception("VeiculosSPM.falhageral<message>" + e.getMessage());
        }
    }

    public int cadastrarModelo(VeiculosMod veiculosMod, Persistencia persistencia) throws Exception {
        try {
            VeiculosModDao veiculosModDao = new VeiculosModDao();
            veiculosModDao.cadastrarModelo(veiculosMod, persistencia);

            VeiculosMod insercao = veiculosModDao.buscarModeloDescricao(veiculosMod.getDescricao(), persistencia);
            if (insercao == null) {
                return 0;
            }
            return insercao.getCodigo();
        } catch (Exception e) {
            throw new Exception("VeiculosSPM.falhageral<message>" + e.getMessage());
        }
    }

    public List<VeiculosMod> listarModelos(Persistencia persistencia) throws Exception {
        try {
            VeiculosModDao veiculosModDao = new VeiculosModDao();
            return veiculosModDao.listarModelos(persistencia);
        } catch (Exception e) {
            throw new Exception("VeiculosSPM.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            VeiculosDao veiculosDao = new VeiculosDao();
            return veiculosDao.contagem(filtros, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("VeiculosSPM.falhageral<message>" + e.getMessage());
        }
    }

    public List<Veiculos> listagemPaginada(int primeiro, int linhas, BigDecimal codPessoa, Map filtros, Persistencia persistencia) throws Exception {
        try {
            VeiculosDao veiculosDao = new VeiculosDao();
            return veiculosDao.listagemPaginada(primeiro, linhas, codPessoa, filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("VeiculosSPM.falhageral<message>" + e.getMessage());
        }
    }

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("VeiculosSPM.falhageral<message>" + e.getMessage());
        }
    }

}
