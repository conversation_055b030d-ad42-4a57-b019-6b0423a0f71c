
package br.gov.go.goiania.nfse.ws;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.3.0
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "NFSe", targetNamespace = "http://nfse.goiania.go.gov.br/ws/", wsdlLocation = "file:/C:/Users/<USER>/Desktop/nfse-goiania-wsdl.asmx")
public class NFSe
    extends Service
{

    private final static URL NFSE_WSDL_LOCATION;
    private final static WebServiceException NFSE_EXCEPTION;
    private final static QName NFSE_QNAME = new QName("http://nfse.goiania.go.gov.br/ws/", "NFSe");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("file:/C:/Users/<USER>/Desktop/nfse-goiania-wsdl.asmx");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        NFSE_WSDL_LOCATION = url;
        NFSE_EXCEPTION = e;
    }

    public NFSe() {
        super(__getWsdlLocation(), NFSE_QNAME);
    }

    public NFSe(WebServiceFeature... features) {
        super(__getWsdlLocation(), NFSE_QNAME, features);
    }

    public NFSe(URL wsdlLocation) {
        super(wsdlLocation, NFSE_QNAME);
    }

    public NFSe(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, NFSE_QNAME, features);
    }

    public NFSe(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public NFSe(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns NFSeSoap
     */
    @WebEndpoint(name = "NFSeSoap")
    public NFSeSoap getNFSeSoap() {
        return super.getPort(new QName("http://nfse.goiania.go.gov.br/ws/", "NFSeSoap"), NFSeSoap.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns NFSeSoap
     */
    @WebEndpoint(name = "NFSeSoap")
    public NFSeSoap getNFSeSoap(WebServiceFeature... features) {
        return super.getPort(new QName("http://nfse.goiania.go.gov.br/ws/", "NFSeSoap"), NFSeSoap.class, features);
    }

    /**
     * 
     * @return
     *     returns NFSeSoap
     */
    @WebEndpoint(name = "NFSeSoap12")
    public NFSeSoap getNFSeSoap12() {
        return super.getPort(new QName("http://nfse.goiania.go.gov.br/ws/", "NFSeSoap12"), NFSeSoap.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns NFSeSoap
     */
    @WebEndpoint(name = "NFSeSoap12")
    public NFSeSoap getNFSeSoap12(WebServiceFeature... features) {
        return super.getPort(new QName("http://nfse.goiania.go.gov.br/ws/", "NFSeSoap12"), NFSeSoap.class, features);
    }

    private static URL __getWsdlLocation() {
        if (NFSE_EXCEPTION!= null) {
            throw NFSE_EXCEPTION;
        }
        return NFSE_WSDL_LOCATION;
    }

}
