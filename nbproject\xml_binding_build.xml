<?xml version="1.0" encoding="UTF-8"?><!--
            *** GENERATED FROM xml_binding_cfg.xml - DO NOT EDIT  ***
            *** Configure thru JAXB Wizard.                       ***
        --><project name="PacotesUteis_jaxb" default="default" basedir=".">
    <target name="xjc-typedef-target" depends="-init-project">
        <typedef classname="com.sun.tools.xjc.XJCTask" name="xjc" xmlns:s="http://xml.netbeans.org/schema/JAXBWizConfig">
            <classpath path="${jaxbwiz.xjcdef.classpath}"/>
        </typedef>
    </target>
    <target name="jaxb-clean-code-generation" depends="clean,jaxb-code-generation"/>
    <target name="jaxb-code-generation" depends="xjc-typedef-target,-do-init,-init-macrodef-javac">
        <mkdir dir="${build.generated.sources.dir}/jaxb" xmlns:s="http://xml.netbeans.org/schema/JAXBWizConfig"/>
        <mkdir dir="build/generated/jaxbCache" xmlns:s="http://xml.netbeans.org/schema/JAXBWizConfig"/>
    </target>
</project>
