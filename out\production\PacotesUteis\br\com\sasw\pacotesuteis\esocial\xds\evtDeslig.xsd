﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtDeslig/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtDeslig/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtDeslig">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeEveTrab">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideVinculo" type="TIdeVinculoNisObrig">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Trabalhador e do Vinculo</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="infoDeslig">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="mtvDeslig">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>COdigo de Motivo do Desligamento, conforme tabela 19</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="\d{2}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="dtDeslig">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Data do Desligamento</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:date">
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="indPagtoAPI">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Indicativo de pagamento de Aviso Prévio Indenizado pelo empregador, ao empregado:
                                                        S - Sim;
                                                        N - Nao</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="[N|S]"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="dtProjFimAPI" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Data projetada para o término do aviso prévio indenizado</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:date">
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="pensAlim">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Indicativo de pensao para fins de FGTS</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="percAliment" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Percentual a ser destinado a pensao alimenticia</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:decimal">
                                                    <xs:totalDigits value="5"/>
                                                    <xs:fractionDigits value="2"/>
                                                    <xs:maxInclusive value="100"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="vrAlim" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Valor da pensao alimenticia</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:decimal">
                                                    <xs:totalDigits value="14"/>
                                                    <xs:fractionDigits value="2"/>
                                                    <xs:maxInclusive value="999999999999"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nrCertObito" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Numero da certidao de Obito</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="\d{32}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nrProcTrab" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Numero que identifica o processo trabalhista, quando o desligamento se der por decisao judicial</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:length value="20"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="indCumprParc">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Cumprimento parcial de aviso prévio</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="qtdDiasInterm" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Quantidade de dias trabalhados - contrato intermitente</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d{1,2}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="observacoes" minOccurs="0" maxOccurs="99">
                                            <xs:annotation>
                                                <xs:documentation>Observacoes sobre o desligamento</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="observacao">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Observacao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="2"/>
                                                                <xs:maxLength value="255"/>
                                                                <xs:whiteSpace value="preserve"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="sucessaoVinc" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Sucessao do Vinculo Trabalhista/Estatutario</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="tpInscSuc">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Tipo de inscricao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="cnpjSucessora">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>CNPJ da empresa sucessora</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="\d{8,14}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="transfTit" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Transferencia de titularidade do empregado doméstico</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="cpfSubstituto">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>CPF do novo titular</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="\d{11}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="dtNascto">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Data de Nascimento</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:date">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="mudancaCPF" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacao do novo CPF do trabalhador</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="novoCPF">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Novo CPF do trabalhador</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="\d{11}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="verbasResc" minOccurs="0">
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="dmDev" maxOccurs="50">
                                                        <xs:annotation>
                                                            <xs:documentation>Demonstrativos de valores devidos ao trabalhador</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="ideDmDev">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Identificador atribuido pela empresa para o demonstrativo de pagamento (contracheque/holerite) conforme definido em S-1200</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="30"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="infoPerApur" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Verbas rescisOrias relativas ao mes da data do desligamento</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="ideEstabLot" maxOccurs="24">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Identificacao do estabelecimento e lotacao</xs:documentation>
                                                                                </xs:annotation>
                                                                                <xs:complexType>
                                                                                    <xs:sequence>
                                                                                        <xs:element name="tpInsc">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:byte">
                                                                                                    <xs:pattern value="\d"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="nrInsc">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Numero de Inscricao</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:string">
                                                                                                    <xs:pattern value="\d{8,14}"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="codLotacao">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>COdigo da Lotacao</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:string">
                                                                                                    <xs:minLength value="1"/>
                                                                                                    <xs:maxLength value="30"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="detVerbas" maxOccurs="200">
                                                                                            <xs:annotation>
                                                                                                <xs:documentation>Detalhamento das verbas rescisOrias</xs:documentation>
                                                                                            </xs:annotation>
                                                                                            <xs:complexType>
                                                                                                <xs:sequence>
                                                                                                    <xs:element name="codRubr">
                                                                                                        <xs:simpleType>
                                                                                                            <xs:annotation>
                                                                                                                <xs:documentation>COdigo da Rubrica</xs:documentation>
                                                                                                            </xs:annotation>
                                                                                                            <xs:restriction base="xs:string">
                                                                                                                <xs:minLength value="1"/>
                                                                                                                <xs:maxLength value="30"/>
                                                                                                            </xs:restriction>
                                                                                                        </xs:simpleType>
                                                                                                    </xs:element>
                                                                                                    <xs:element name="ideTabRubr">
                                                                                                        <xs:simpleType>
                                                                                                            <xs:annotation>
                                                                                                                <xs:documentation>Identificador da tabela de rubricas</xs:documentation>
                                                                                                            </xs:annotation>
                                                                                                            <xs:restriction base="xs:string">
                                                                                                                <xs:minLength value="1"/>
                                                                                                                <xs:maxLength value="8"/>
                                                                                                            </xs:restriction>
                                                                                                        </xs:simpleType>
                                                                                                    </xs:element>
                                                                                                    <xs:element name="qtdRubr" minOccurs="0">
                                                                                                        <xs:simpleType>
                                                                                                            <xs:annotation>
                                                                                                                <xs:documentation>Qtde de referencia para apuracao da rubrica</xs:documentation>
                                                                                                            </xs:annotation>
                                                                                                            <xs:restriction base="xs:decimal">
                                                                                                                <xs:totalDigits value="6"/>
                                                                                                                <xs:fractionDigits value="2"/>
                                                                                                                <xs:maxInclusive value="9999.99"/>
                                                                                                            </xs:restriction>
                                                                                                        </xs:simpleType>
                                                                                                    </xs:element>
                                                                                                    <xs:element name="fatorRubr" minOccurs="0">
                                                                                                        <xs:simpleType>
                                                                                                            <xs:annotation>
                                                                                                                <xs:documentation>Fator utilizado na apuracao da rubrica</xs:documentation>
                                                                                                            </xs:annotation>
                                                                                                            <xs:restriction base="xs:decimal">
                                                                                                                <xs:totalDigits value="5"/>
                                                                                                                <xs:fractionDigits value="2"/>
                                                                                                                <xs:maxInclusive value="999.99"/>
                                                                                                            </xs:restriction>
                                                                                                        </xs:simpleType>
                                                                                                    </xs:element>
                                                                                                    <xs:element name="vrUnit" minOccurs="0">
                                                                                                        <xs:simpleType>
                                                                                                            <xs:annotation>
                                                                                                                <xs:documentation>Valor Unitario</xs:documentation>
                                                                                                            </xs:annotation>
                                                                                                            <xs:restriction base="xs:decimal">
                                                                                                                <xs:totalDigits value="14"/>
                                                                                                                <xs:fractionDigits value="2"/>
                                                                                                                <xs:maxInclusive value="999999999999"/>
                                                                                                            </xs:restriction>
                                                                                                        </xs:simpleType>
                                                                                                    </xs:element>
                                                                                                    <xs:element name="vrRubr">
                                                                                                        <xs:simpleType>
                                                                                                            <xs:annotation>
                                                                                                                <xs:documentation>Valor total da rubrica</xs:documentation>
                                                                                                            </xs:annotation>
                                                                                                            <xs:restriction base="xs:decimal">
                                                                                                                <xs:totalDigits value="14"/>
                                                                                                                <xs:fractionDigits value="2"/>
                                                                                                                <xs:maxInclusive value="999999999999"/>
                                                                                                            </xs:restriction>
                                                                                                        </xs:simpleType>
                                                                                                    </xs:element>
                                                                                                </xs:sequence>
                                                                                            </xs:complexType>
                                                                                        </xs:element>
                                                                                        <xs:element name="infoSaudeColet" type="TSaudeCol" minOccurs="0">
                                                                                            <xs:annotation>
                                                                                                <xs:documentation>Informacoes de plano privado coletivo empresarial de assistencia à saude</xs:documentation>
                                                                                            </xs:annotation>
                                                                                        </xs:element>
                                                                                        <xs:element name="infoAgNocivo" minOccurs="0">
                                                                                            <xs:annotation>
                                                                                                <xs:documentation>Grau de Exposicao a Agentes Nocivos</xs:documentation>
                                                                                            </xs:annotation>
                                                                                            <xs:complexType>
                                                                                                <xs:sequence>
                                                                                                    <xs:element name="grauExp">
                                                                                                        <xs:simpleType>
                                                                                                            <xs:annotation>
                                                                                                                <xs:documentation>Grau de exposicao a agentes nocivos, conforme tabela 2</xs:documentation>
                                                                                                            </xs:annotation>
                                                                                                            <xs:restriction base="xs:byte">
                                                                                                                <xs:pattern value="\d"/>
                                                                                                            </xs:restriction>
                                                                                                        </xs:simpleType>
                                                                                                    </xs:element>
                                                                                                </xs:sequence>
                                                                                            </xs:complexType>
                                                                                        </xs:element>
                                                                                        <xs:element name="infoSimples" minOccurs="0">
                                                                                            <xs:annotation>
                                                                                                <xs:documentation>Informacao relativa a empresas do Simples</xs:documentation>
                                                                                            </xs:annotation>
                                                                                            <xs:complexType>
                                                                                                <xs:sequence>
                                                                                                    <xs:element name="indSimples">
                                                                                                        <xs:simpleType>
                                                                                                            <xs:annotation>
                                                                                                                <xs:documentation>Indicador de Contribuicao Substituida</xs:documentation>
                                                                                                            </xs:annotation>
                                                                                                            <xs:restriction base="xs:byte">
                                                                                                                <xs:pattern value="\d"/>
                                                                                                            </xs:restriction>
                                                                                                        </xs:simpleType>
                                                                                                    </xs:element>
                                                                                                </xs:sequence>
                                                                                            </xs:complexType>
                                                                                        </xs:element>
                                                                                    </xs:sequence>
                                                                                </xs:complexType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                                <xs:element name="infoPerAnt" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Remuneracao em Periodos Anteriores (Acordo/Convencao/CCP/Dissidio)</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="ideADC" maxOccurs="8">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Identificacao do Acordo/Legislacao/Convencao/CCP/Dissidio/Conversao</xs:documentation>
                                                                                </xs:annotation>
                                                                                <xs:complexType>
                                                                                    <xs:sequence>
                                                                                        <xs:element name="dtAcConv">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Data da assinatura do acordo/convencao</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:date">
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="tpAcConv">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Tipo do instrumento ou situacao</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:string">
                                                                                                    <xs:minLength value="1"/>
                                                                                                    <xs:maxLength value="1"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="compAcConv" minOccurs="0">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Competencia</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:string">
                                                                                                    <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="dtEfAcConv">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Data dos efeitos do acordo/convencao</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:date">
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="dsc">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Descricao</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:string">
                                                                                                    <xs:minLength value="2"/>
                                                                                                    <xs:maxLength value="255"/>
                                                                                                    <xs:whiteSpace value="preserve"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="idePeriodo" maxOccurs="180">
                                                                                            <xs:annotation>
                                                                                                <xs:documentation>Identificacao do periodo de referencia da remuneracao</xs:documentation>
                                                                                            </xs:annotation>
                                                                                            <xs:complexType>
                                                                                                <xs:sequence>
                                                                                                    <xs:element name="perRef">
                                                                                                        <xs:simpleType>
                                                                                                            <xs:annotation>
                                                                                                                <xs:documentation>Periodo de referencia</xs:documentation>
                                                                                                            </xs:annotation>
                                                                                                            <xs:restriction base="xs:string">
                                                                                                                <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                                                                            </xs:restriction>
                                                                                                        </xs:simpleType>
                                                                                                    </xs:element>
                                                                                                    <xs:element name="ideEstabLot" maxOccurs="24">
                                                                                                        <xs:annotation>
                                                                                                            <xs:documentation>Identificacao do estabelecimento e lotacao</xs:documentation>
                                                                                                        </xs:annotation>
                                                                                                        <xs:complexType>
                                                                                                            <xs:sequence>
                                                                                                                <xs:element name="tpInsc">
                                                                                                                    <xs:simpleType>
                                                                                                                        <xs:annotation>
                                                                                                                            <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                                                                                        </xs:annotation>
                                                                                                                        <xs:restriction base="xs:byte">
                                                                                                                            <xs:pattern value="\d"/>
                                                                                                                        </xs:restriction>
                                                                                                                    </xs:simpleType>
                                                                                                                </xs:element>
                                                                                                                <xs:element name="nrInsc">
                                                                                                                    <xs:simpleType>
                                                                                                                        <xs:annotation>
                                                                                                                            <xs:documentation>Numero de Inscricao</xs:documentation>
                                                                                                                        </xs:annotation>
                                                                                                                        <xs:restriction base="xs:string">
                                                                                                                            <xs:pattern value="\d{8,14}"/>
                                                                                                                        </xs:restriction>
                                                                                                                    </xs:simpleType>
                                                                                                                </xs:element>
                                                                                                                <xs:element name="codLotacao">
                                                                                                                    <xs:simpleType>
                                                                                                                        <xs:annotation>
                                                                                                                            <xs:documentation>COdigo da Lotacao</xs:documentation>
                                                                                                                        </xs:annotation>
                                                                                                                        <xs:restriction base="xs:string">
                                                                                                                            <xs:minLength value="1"/>
                                                                                                                            <xs:maxLength value="30"/>
                                                                                                                        </xs:restriction>
                                                                                                                    </xs:simpleType>
                                                                                                                </xs:element>
                                                                                                                <xs:element name="detVerbas" maxOccurs="200">
                                                                                                                    <xs:annotation>
                                                                                                                        <xs:documentation>Itens da Remuneracao do Trabalhador</xs:documentation>
                                                                                                                    </xs:annotation>
                                                                                                                    <xs:complexType>
                                                                                                                        <xs:sequence>
                                                                                                                            <xs:element name="codRubr">
                                                                                                                                <xs:simpleType>
                                                                                                                                    <xs:annotation>
                                                                                                                                        <xs:documentation>COdigo da Rubrica</xs:documentation>
                                                                                                                                    </xs:annotation>
                                                                                                                                    <xs:restriction base="xs:string">
                                                                                                                                        <xs:minLength value="1"/>
                                                                                                                                        <xs:maxLength value="30"/>
                                                                                                                                    </xs:restriction>
                                                                                                                                </xs:simpleType>
                                                                                                                            </xs:element>
                                                                                                                            <xs:element name="ideTabRubr">
                                                                                                                                <xs:simpleType>
                                                                                                                                    <xs:annotation>
                                                                                                                                        <xs:documentation>Identificador da tabela de rubricas</xs:documentation>
                                                                                                                                    </xs:annotation>
                                                                                                                                    <xs:restriction base="xs:string">
                                                                                                                                        <xs:minLength value="1"/>
                                                                                                                                        <xs:maxLength value="8"/>
                                                                                                                                    </xs:restriction>
                                                                                                                                </xs:simpleType>
                                                                                                                            </xs:element>
                                                                                                                            <xs:element name="qtdRubr" minOccurs="0">
                                                                                                                                <xs:simpleType>
                                                                                                                                    <xs:annotation>
                                                                                                                                        <xs:documentation>Qtde de referencia para apuracao da rubrica</xs:documentation>
                                                                                                                                    </xs:annotation>
                                                                                                                                    <xs:restriction base="xs:decimal">
                                                                                                                                        <xs:totalDigits value="6"/>
                                                                                                                                        <xs:fractionDigits value="2"/>
                                                                                                                                        <xs:maxInclusive value="9999.99"/>
                                                                                                                                    </xs:restriction>
                                                                                                                                </xs:simpleType>
                                                                                                                            </xs:element>
                                                                                                                            <xs:element name="fatorRubr" minOccurs="0">
                                                                                                                                <xs:simpleType>
                                                                                                                                    <xs:annotation>
                                                                                                                                        <xs:documentation>Fator utilizado na apuracao da rubrica</xs:documentation>
                                                                                                                                    </xs:annotation>
                                                                                                                                    <xs:restriction base="xs:decimal">
                                                                                                                                        <xs:totalDigits value="5"/>
                                                                                                                                        <xs:fractionDigits value="2"/>
                                                                                                                                        <xs:maxInclusive value="999.99"/>
                                                                                                                                    </xs:restriction>
                                                                                                                                </xs:simpleType>
                                                                                                                            </xs:element>
                                                                                                                            <xs:element name="vrUnit" minOccurs="0">
                                                                                                                                <xs:simpleType>
                                                                                                                                    <xs:annotation>
                                                                                                                                        <xs:documentation>Valor Unitario</xs:documentation>
                                                                                                                                    </xs:annotation>
                                                                                                                                    <xs:restriction base="xs:decimal">
                                                                                                                                        <xs:totalDigits value="14"/>
                                                                                                                                        <xs:fractionDigits value="2"/>
                                                                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                                                                    </xs:restriction>
                                                                                                                                </xs:simpleType>
                                                                                                                            </xs:element>
                                                                                                                            <xs:element name="vrRubr">
                                                                                                                                <xs:simpleType>
                                                                                                                                    <xs:annotation>
                                                                                                                                        <xs:documentation>Valor total da rubrica</xs:documentation>
                                                                                                                                    </xs:annotation>
                                                                                                                                    <xs:restriction base="xs:decimal">
                                                                                                                                        <xs:totalDigits value="14"/>
                                                                                                                                        <xs:fractionDigits value="2"/>
                                                                                                                                        <xs:maxInclusive value="999999999999"/>
                                                                                                                                    </xs:restriction>
                                                                                                                                </xs:simpleType>
                                                                                                                            </xs:element>
                                                                                                                        </xs:sequence>
                                                                                                                    </xs:complexType>
                                                                                                                </xs:element>
                                                                                                                <xs:element name="infoAgNocivo" minOccurs="0">
                                                                                                                    <xs:annotation>
                                                                                                                        <xs:documentation>Grau de Exposicao a Agentes Nocivos</xs:documentation>
                                                                                                                    </xs:annotation>
                                                                                                                    <xs:complexType>
                                                                                                                        <xs:sequence>
                                                                                                                            <xs:element name="grauExp">
                                                                                                                                <xs:simpleType>
                                                                                                                                    <xs:annotation>
                                                                                                                                        <xs:documentation>Grau de exposicao a agentes nocivos, conforme tabela 2</xs:documentation>
                                                                                                                                    </xs:annotation>
                                                                                                                                    <xs:restriction base="xs:byte">
                                                                                                                                        <xs:pattern value="\d"/>
                                                                                                                                    </xs:restriction>
                                                                                                                                </xs:simpleType>
                                                                                                                            </xs:element>
                                                                                                                        </xs:sequence>
                                                                                                                    </xs:complexType>
                                                                                                                </xs:element>
                                                                                                                <xs:element name="infoSimples" minOccurs="0">
                                                                                                                    <xs:annotation>
                                                                                                                        <xs:documentation>Informacao relativa a empresas do Simples</xs:documentation>
                                                                                                                    </xs:annotation>
                                                                                                                    <xs:complexType>
                                                                                                                        <xs:sequence>
                                                                                                                            <xs:element name="indSimples">
                                                                                                                                <xs:simpleType>
                                                                                                                                    <xs:annotation>
                                                                                                                                        <xs:documentation>Indicador de Contribuicao Substituida</xs:documentation>
                                                                                                                                    </xs:annotation>
                                                                                                                                    <xs:restriction base="xs:byte">
                                                                                                                                        <xs:pattern value="\d"/>
                                                                                                                                    </xs:restriction>
                                                                                                                                </xs:simpleType>
                                                                                                                            </xs:element>
                                                                                                                        </xs:sequence>
                                                                                                                    </xs:complexType>
                                                                                                                </xs:element>
                                                                                                            </xs:sequence>
                                                                                                        </xs:complexType>
                                                                                                    </xs:element>
                                                                                                </xs:sequence>
                                                                                            </xs:complexType>
                                                                                        </xs:element>
                                                                                    </xs:sequence>
                                                                                </xs:complexType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                                <xs:element name="infoTrabInterm" minOccurs="0" maxOccurs="99">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Informacoes da(s) convocacao(oes) de trabalho intermitente</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="codConv">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>COdigo que identifica a convocacao para trabalho intermitente</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="2"/>
                                                                                        <xs:maxLength value="30"/>
                                                                                        <xs:whiteSpace value="preserve"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="procJudTrab" minOccurs="0" maxOccurs="99">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes sobre a existencia de processos judiciais do trabalhador</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="tpTrib">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Abrangencia pela decisao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="nrProcJud">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Nr Processo Judicial</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:length value="20"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="codSusp" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo do Indicativo da Suspensao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:integer">
                                                                            <xs:pattern value="\d{1,14}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="infoMV" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacao de Multiplos Vinculos</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="indMV">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Indicador de MV</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="remunOutrEmpr" type="TRemunOutrasEmpresas" maxOccurs="999">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Remuneracao recebida pelo trabalhador em outras empresas ou atividades</xs:documentation>
                                                                    </xs:annotation>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="procCS" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacao sobre processo judicial que suspende a exigibilidade da Contribuicao Social RescisOria</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="nrProcJud">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Nr Processo Judicial</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:length value="20"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="quarentena" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes sobre a quarentena remunerada de trabalhador desligado</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="dtFimQuar">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Preencher com a data final da quarentena a que esta sujeito o trabalhador</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:date">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="consigFGTS" minOccurs="0" maxOccurs="99">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes sobre operacao de crédito consignado com garantia de FGTS</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="insConsig">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Matricula da Instituicao Consignataria cadastrada na CEF</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="1"/>
                                                                <xs:maxLength value="5"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="nrContr">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>N° do contr. de empréstimo consignado existente na Instituicao Consignataria</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="\d{1,40}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeEveTrab">
        <xs:annotation>
            <xs:documentation>Identificacao do evento</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="indRetif">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Retificacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrRecibo" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Numero do recibo do arquivo a ser retificado</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="40"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Numero de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TIdeVinculoNisObrig">
        <xs:annotation>
            <xs:documentation>Informacoes do Vinculo</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cpfTrab">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>CPF do trabalhador</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:length value="11"/>
                        <xs:pattern value="\d{11}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nisTrab">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>NIS</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="11"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="matricula">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Matricula</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="30"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TSaudeCol">
        <xs:annotation>
            <xs:documentation>Planos de saude coletivo</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="detOper" maxOccurs="99">
                <xs:annotation>
                    <xs:documentation>Detalhamento dos valores pagos a Operadoras de Planos de Saude</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="cnpjOper">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>CNPJ de Operadora do Plano de Saude</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:length value="14"/>
                                    <xs:pattern value="\d{14}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="regANS">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Registro da operadora na Agencia Nacional de Saude</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:length value="6"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="vrPgTit">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Valor pago pelo Titular</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="14"/>
                                    <xs:fractionDigits value="2"/>
                                    <xs:maxInclusive value="999999999999"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="detPlano" minOccurs="0" maxOccurs="99">
                            <xs:annotation>
                                <xs:documentation>Informacoes do dependente do plano privado de saude</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="tpDep">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Tipo de dependente</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:length value="2"/>
                                                <xs:pattern value="\d{2}"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="cpfDep" minOccurs="0">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Numero de Inscricao no CPF</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:pattern value="\d{11}"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="nmDep">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Nome do Dependente</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:minLength value="2"/>
                                                <xs:maxLength value="70"/>
                                                <xs:whiteSpace value="preserve"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="dtNascto">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Data de Nascimento</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:date">
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="vlrPgDep">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Valor pago relativo ao dependente</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:decimal">
                                                <xs:totalDigits value="14"/>
                                                <xs:fractionDigits value="2"/>
                                                <xs:maxInclusive value="999999999999"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TRemunOutrasEmpresas">
        <xs:annotation>
            <xs:documentation>Remuneracao em outras empresas ou atividades</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Numero de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="codCateg">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo da Categoria</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:integer">
                        <xs:pattern value="\d{3}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="vlrRemunOE">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Valor da remuneracao recebida pelo trabalhador</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="14"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="999999999999"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
