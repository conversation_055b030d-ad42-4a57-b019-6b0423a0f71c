# Configurações para testes do sistema de emails

# Desabilitar scheduler durante testes
email.scheduler-enabled=false

# Configurações de email para teste (não envia emails reais)
email.default-smtp=localhost
email.default-username=test
email.default-password=test
email.default-port=25
email.default-from-email=test@localhost
email.default-from-name=Test

# Configurações de anexos para teste
email.anexo-base-path=src/test/resources/anexos/
email.max-anexo-size-mb=1

# Configurações de log para teste
email.log-base-path=target/test-logs/
email.log-emails-enabled=false

# Configurações de batch menores para teste
email.batch-size=2
email.process-in-batches=true

# Banco de dados em memória para testes (H2)
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA para testes
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect

# Logs para debug durante testes
logging.level.com.br.sasw.esocial_novo=DEBUG
logging.level.org.springframework.mail=DEBUG
logging.level.org.hibernate.SQL=DEBUG
