package com.br.sasw.esocial_novo.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.io.File;

@Entity
@Table(name = "EmailsEnviarAnexo")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmailsEnviarAnexo {

    @Id
    @Column(name = "Sequencia")
    private BigDecimal sequencia;

    @Column(name = "Ordem")
    private Integer ordem;

    @Column(name = "EndAnexo", length = 500)
    private String endAnexo;

    @Column(name = "DescAnexo", length = 200)
    private String descAnexo;

    @Column(name = "NomeAnexo", length = 200)
    private String nomeAnexo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "Sequencia", insertable = false, updatable = false)
    private EmailsEnviar emailsEnviar;

}
