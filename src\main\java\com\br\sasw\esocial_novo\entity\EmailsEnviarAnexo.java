package com.br.sasw.esocial_novo.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.io.File;

/**
 * Entidade que representa a tabela EmailsEnviarAnexo
 * Migrada do projeto satelliteserver para uma estrutura moderna com JPA
 */
@Entity
@Table(name = "EmailsEnviarAnexo")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmailsEnviarAnexo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "Sequencia")
    private BigDecimal sequencia;

    @Column(name = "Ordem")
    private Integer ordem;

    @Column(name = "EndAnexo", length = 500)
    private String endAnexo;

    @Column(name = "DescAnexo", length = 200)
    private String descAnexo;

    @Column(name = "NomeAnexo", length = 200)
    private String nomeAnexo;

    // Relacionamento com o email principal
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "Sequencia", insertable = false, updatable = false)
    private EmailsEnviar emailsEnviar;

    // Métodos de conveniência
    public String getCaminhoCompletoAnexo() {
        if (endAnexo == null || endAnexo.trim().isEmpty()) {
            return null;
        }
        
        // Caminho base configurável via properties
        String caminhoBase = "C:\\xampp\\htdocs\\satmobile\\documentos\\anexo-email\\";
        return caminhoBase + endAnexo;
    }

    public boolean anexoExiste() {
        String caminhoCompleto = getCaminhoCompletoAnexo();
        if (caminhoCompleto == null) {
            return false;
        }
        
        File arquivo = new File(caminhoCompleto);
        return arquivo.exists() && arquivo.isFile();
    }

    public long getTamanhoArquivo() {
        String caminhoCompleto = getCaminhoCompletoAnexo();
        if (caminhoCompleto == null) {
            return 0;
        }
        
        File arquivo = new File(caminhoCompleto);
        return arquivo.exists() ? arquivo.length() : 0;
    }
}
