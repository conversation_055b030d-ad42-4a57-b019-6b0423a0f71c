﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtTabAmbiente/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtTabAmbiente/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtTabAmbiente">
                    <xs:annotation>
                        <xs:documentation>Evento Tabela de Ambientes de Trabalho</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeCadastro">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="infoAmbiente">
                                <xs:annotation>
                                    <xs:documentation>Informacoes do Ambiente</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:choice>
                                            <xs:element name="inclusao">
                                                <xs:annotation>
                                                    <xs:documentation>Inclusao de novas informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideAmbiente" type="TIdeAmbiente">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes de identificacao do ambiente de trabalho do empregador</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="dadosAmbiente" type="TDadosAmbiente">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes do ambiente de trabalho</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="alteracao">
                                                <xs:annotation>
                                                    <xs:documentation>Alteracao das informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideAmbiente" type="TIdeAmbiente">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes de identificacao do Ambiente</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="dadosAmbiente" type="TDadosAmbiente">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes do Ambiente de Trabalho</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="novaValidade" type="TPeriodoValidade" minOccurs="0">
                                                            <xs:annotation>
                                                                <xs:documentation>Novo periodo de validade das informacoes</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="exclusao">
                                                <xs:annotation>
                                                    <xs:documentation>Exclusao das informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideAmbiente" type="TIdeAmbiente">
                                                            <xs:annotation>
                                                                <xs:documentation>Identificacao do Ambiente que sera excluido</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                        </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeCadastro">
        <xs:annotation>
            <xs:documentation>Identificacao de evento de cadastro/tabelas</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TIdeAmbiente">
        <xs:annotation>
            <xs:documentation>Informacoes de identificacao do Ambiente de Trabalho</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="codAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Preencher com o cOdigo atribuido pela empresa ao Ambiente de Trabalho</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="30"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="iniValid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Inicio da validade (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fimValid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo final da validade, no formato (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TDadosAmbiente">
        <xs:annotation>
            <xs:documentation>Informacoes do ambiente de trabalho</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="nmAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Nome do ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="100"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dscAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Descricao do ambiente de trabalho</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="3"/>
                        <xs:maxLength value="8000"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="localAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Local do ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpInsc" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="codLotacao" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo da Lotacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="30"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TPeriodoValidade">
        <xs:annotation>
            <xs:documentation>Periodo de validade das informacoes</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="iniValid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Inicio da validade (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fimValid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo final da validade, no formato (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
