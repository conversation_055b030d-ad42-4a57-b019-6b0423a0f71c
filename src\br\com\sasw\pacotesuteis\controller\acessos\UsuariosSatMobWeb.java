package br.com.sasw.pacotesuteis.controller.acessos;

import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.PessoaLogin;
import SasBeans.SASGrupos;
import SasBeans.SasPWFill;
import SasBeans.Saspw;
import SasBeans.Saspwac;
import SasBeans.Sysdef;
import SasBeansCompostas.SaspwacSysdef;
import SasDaos.AcessosDao;
import SasDaos.ClientesDao;
import SasDaos.PessoaDao;
import SasDaos.PessoaLoginDao;
import SasDaos.SASGruposDao;
import SasDaos.SasPwFilDao;
import SasDaos.SaspwDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR> Maracaipe
 */
public class UsuariosSatMobWeb {

    /**
     * Lista de acesso
     *
     * @param codfil Código da filial
     * @param persistencia Conexao com a base de dados
     * @return lista contendo os registros dos acessos
     * @throws Exception
     */
    public List<Saspw> listaAcessos(BigDecimal codfil, Persistencia persistencia) throws Exception {
        List<Saspw> acessos = new ArrayList();
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessos = acessosDAO.listaUsuarios(codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
        return acessos;
    }

    /**
     * Lista de acesso
     *
     * @param codfil Código da filial
     * @param persistencia Conexao com a base de dados
     * @return lista contendo os registros dos acessos
     * @throws Exception
     */
    public Integer contaAcessos(BigDecimal codfil, Persistencia persistencia) throws Exception {
        int acessos = 0;
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessos = acessosDAO.contaUsuarios(codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
        return acessos;
    }

    /**
     * Lista de acesso
     *
     * @param acesso Acesso com os parametros a serem pesquisados
     * @param codPessoa
     * @param persistencia Conexao com a base de dados
     * @return lista contendo os registros dos acessos
     * @throws Exception
     */
    public List<Saspw> pesquisaAcessos(Saspw acesso, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        List<Saspw> acessos = new ArrayList();
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessos = acessosDAO.pesquisaUsuarios(acesso, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
        return acessos;
    }

    /**
     * Inseri filial no usuario
     *
     * @param filial - Objeto contendo informações sobre
     * @param persistencia Conexão com base de dados
     * @throws Exception
     */
    public void inserirFilial(SasPWFill filial, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            filial = (SasPWFill) FuncoesString.removeAcentoObjeto(filial);
            filial.setDt_Alter(DataAtual.getDataAtual("SQL"));
            filial.setHr_Alter(DataAtual.getDataAtual("HORA"));

            acessosDAO.inserirFilial(filial, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirCliente(PessoaCliAut cliente, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            Boolean existeCliente = acessosDAO.existeCliente(cliente, persistencia);
            if (existeCliente) {
                acessosDAO.atualizaCliente(cliente, persistencia);
            } else {
                acessosDAO.inserirCliente(cliente, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Cria permissoes para pessoa
     *
     * @param usuario Saspw a ser cadastrado
     * @param acesso
     * @param banco Nome da empressa
     * @param local Conexão com a base de dados local
     * @param central Conexão com a base de dados central
     *
     * @throws Exception
     */
    public void criarAcesso(Saspw usuario, Persistencia local, Persistencia central) throws Exception {
        try {
            usuario = (Saspw) FuncoesString.removeAcentoObjeto(usuario);
            usuario.setDt_Alter(DataAtual.getDataAtual("SQL"));
            usuario.setHr_Alter(DataAtual.getDataAtual("HORA"));

            AcessosDao acessosDAO = new AcessosDao();
            PessoaLoginDao pessoalogindao = new PessoaLoginDao();
            PessoaDao pessoadao = new PessoaDao();
            PessoaLogin pessoalogin = new PessoaLogin();

            if (acessosDAO.existeUsuario(usuario, local)) {
                acessosDAO.editarUsuario(usuario, local);
            } else {
                acessosDAO.criarUsuario(usuario, local);
            }

            Pessoa pessoa = new Pessoa();
            pessoa.setPWWeb(usuario.getPW());
            pessoa.setCodigo(usuario.getCodPessoaWeb());
            pessoa.setDt_Alter(getDataAtual("SQL"));
            pessoa.setHr_Alter(getDataAtual("HORA"));
            pessoadao.atualizaSenhaSatMob(pessoa, central);
            pessoa.setCodigo(usuario.getCodPessoa());
            pessoadao.atualizaSenhaSatMob(pessoa, local);

            pessoalogin.setCodigo(usuario.getCodPessoaWeb());
            pessoalogin.setBancoDados(local.getEmpresa());
            pessoalogin.setCodPessoaBD(usuario.getCodPessoa());
            pessoalogin.setNivel(usuario.getNivelx());

            if (pessoalogindao.existePessoaLogin(pessoalogin, central)) {
                pessoalogindao.atualizaPessoaLogin(pessoalogin, central);
            } else {
                pessoalogindao.gravaPessoaLogin(pessoalogin, central);
            }
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void TrocarSenhaCliente(BigDecimal codPessoa, String senha, Persistencia local, Persistencia central) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            Saspw acesso = acessosdao.getUsuario(codPessoa, local);
            acesso.setPW(senha);
            editarAcesso(acesso, local, central);
        } catch (Exception e) {
            throw new Exception("login.falhaalterarsenhacliente<message>" + e.getMessage());
        }
    }

    /**
     * Edita acesso e atualiza a senha da pessoa nas bases local e central;
     *
     * @param acesso Obejto Saspw
     * @param usuario
     * @param local Conexão com a base de dados local
     * @param central Conexão com a base de dados central
     * @throws Exception
     */
    public void editarAcesso(Saspw acesso, Persistencia local, Persistencia central) throws Exception {
        try {
            // acesso.setPW(SatCripto.Criptografar(acesso.getPW(), "Active Solutions SAS Systems"));
            acesso.setDt_Alter(DataAtual.getDataAtual("SQL"));
            acesso.setHr_Alter(DataAtual.getDataAtual("HORA"));

            AcessosDao acessosDAO = new AcessosDao();
            acessosDAO.editarUsuario(acesso, local);

            PessoaDao pessoadao = new PessoaDao();
            Pessoa pessoa = new Pessoa();
            pessoa.setOperador(acesso.getOperador());
            pessoa.setPWWeb(acesso.getPW());
            pessoa.setCodigo(acesso.getCodPessoaWeb());
            pessoa.setDt_Alter(getDataAtual("SQL"));
            pessoa.setHr_Alter(getDataAtual("HORA"));
            pessoadao.atualizaSenhaSatMob(pessoa, central);
            pessoa.setCodigo(acesso.getCodPessoa());
            pessoadao.atualizaSenhaSatMob(pessoa, local);

            PessoaLoginDao pessoalogindao = new PessoaLoginDao();
            PessoaLogin pessoalogin = new PessoaLogin();
            pessoalogin.setCodigo(acesso.getCodPessoaWeb());
            pessoalogin.setCodPessoaBD(acesso.getCodPessoa());
            pessoalogin.setNivel(acesso.getNivelx());
            pessoalogindao.atualizaPessoaLogin(pessoalogin, central);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Cria permissâo individuais para usuário
     *
     * @param permissao Lista contendo informações das permissões
     * @param persistencia Conexão com o banco de dados
     * @exception Exception
     */
    public void criarPermissoesIndividuais(Saspwac permissao, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            permissao = (Saspwac) FuncoesString.removeAcentoObjeto(permissao);
            acessosDAO.criarPermissoesIndividuais(permissao, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Edita permissões individuais
     *
     * @param permissao permissão que deseja editar
     * @param persistencia Conexao com o banco de dados
     * @throws Exception
     */
    public void editarPermissoesIndividuais(Saspwac permissao, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            permissao = (Saspwac) FuncoesString.removeAcentoObjeto(permissao);
            acessosDAO.editarPermissoesIndividuais(permissao, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Apaga o registro da filial da base de dados
     *
     * @param nome Nome do usuario
     * @param codfilAc
     * @param persistencia Conexão com a base de dados
     * @throws Exception
     */
    public void apagarFilial(String nome, String codfilAc, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessosDAO.apagarRegistroFilial(nome, codfilAc, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public void apagarCliente(PessoaCliAut cliente, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessosDAO.apagarRegistroCliente(cliente, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Apaga o registro da permissao
     *
     * @param nome Nome do usuario
     * @param sistema código do sistema
     * @param persistencia conexão com a base de dados
     * @throws Exception
     */
    public void apagarPermissoes(String nome, String sistema, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessosDAO.apagarRegistroPermissao(nome, sistema, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Bloquea o usuário
     *
     * @param nome nome do usuário
     * @param persistencia conexão com base de dados
     * @throws Exception
     */
    public void bloquearUsuario(String nome, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosDAO = new AcessosDao();
            acessosDAO.bloquearUsuario(nome, persistencia);
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    //Realiza o tratamento do texto
    private String tratarValores(String texto) {
        if (texto.contains("-")) {
            texto = texto.replace("-", "");
        }
        return texto;
    }

    public List<SASGrupos> ListarGrupos(Persistencia persistencia) throws Exception {
        try {
            SASGruposDao sasgruposdao = new SASGruposDao();
            List<SASGrupos> grupos;
            grupos = sasgruposdao.listaSASGrupos(persistencia);
            return grupos;
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<SasPWFill> ListarFiliaisPermissao(String sNome, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            List<SasPWFill> filiais;
            filiais = saspwfildao.getSasPWFill(sNome, persistencia);
            return filiais;
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<SaspwacSysdef> ListarPermissoesUsuario(String nome, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            List<SaspwacSysdef> permissoes;
            permissoes = acessosdao.listaPermissoes(nome, persistencia);
            return permissoes;
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<SasPWFill> ListarTodasFiliais(Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            List<SasPWFill> filiais;
            filiais = acessosdao.listaTodasFiliais(persistencia);
            return filiais;
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public SasPWFill BuscarFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            SasPWFill filial;
            filial = saspwfildao.buscaSasPWFill(CodFil, persistencia);
            return filial;
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<Sysdef> ListarTodasPermissoes(Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            List<Sysdef> permissoes;
            permissoes = acessosdao.listaTodasPermissoes(persistencia);
            return permissoes;
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<Sysdef> ListarPermissoesGrupo(BigDecimal codGrupo, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            List<Sysdef> permissoes;
            permissoes = acessosdao.listaPermissoesGrupo(codGrupo, persistencia);
            return permissoes;
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<PessoaCliAut> ListarClientes(BigDecimal codPessoa, Boolean exclusao, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            List<PessoaCliAut> clientes;
            clientes = acessosdao.listaClientes(codPessoa, exclusao, persistencia);
            return clientes;
        } catch (Exception e) {
            throw new Exception("acessos.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> BuscarClientes(String nome, String query, Persistencia persistencia)
            throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.BuscaClientes(nome, query, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> BuscarClientes(List<SasPWFill> filiais, String query, Persistencia persistencia)
            throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.buscaClientes(filiais, query, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> BuscarClientes(BigDecimal codpessoa, String query, Persistencia persistencia)
            throws Exception {
        try {
            ClientesDao acessosdao = new ClientesDao();
            return acessosdao.QueryCliente(codpessoa, query, persistencia);
        } catch (Exception e) {
            throw new Exception("postoservico.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca a pessoa pelo CodPessoaWeb
     *
     * @param codpesWeb CodPessoaWeb
     * @param persistencia Conexão com o banco
     * @return Retorna pessoa de SASPW
     */
    public Saspw BuscaLogin(BigDecimal codpesWeb, Persistencia persistencia) {
        Saspw retorno = new Saspw();
        SaspwDao saspwDao = new SaspwDao();
        try {
            retorno = saspwDao.BuscaLogin(codpesWeb, persistencia);
        } catch (Exception e) {
        }

        return retorno;
    }

    /* LISTAGENS PAGINADAS */
    /**
     * Contagem do cadastro de usuaarios
     *
     * @param filtros - filtros de pesquisa
     * @param codPessoa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer Contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            AcessosDao acessosdao = new AcessosDao();
            retorno = acessosdao.TotalUsuariosMobWeb(filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de usuarios
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo da pessoa
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Saspw> ListagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {

        try {
            List<Saspw> retorno;
            AcessosDao acessosdao = new AcessosDao();
            retorno = acessosdao.ListaPaginada(primeiro, linhas, filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("acesso.falhageral<message>" + e.getMessage());
        }
    }

    public Saspw SelecionarUsuario(BigDecimal codPessoa, Persistencia local) throws Exception {
        try {
            AcessosDao acessosdao = new AcessosDao();
            return acessosdao.getUsuario(codPessoa, local);
        } catch (Exception e) {
            throw new Exception("login.falhaselecionarusuario<message>" + e.getMessage());
        }
    }
}
