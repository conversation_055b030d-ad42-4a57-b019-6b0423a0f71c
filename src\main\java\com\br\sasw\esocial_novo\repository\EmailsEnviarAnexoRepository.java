package com.br.sasw.esocial_novo.repository;

import com.br.sasw.esocial_novo.entity.EmailsEnviarAnexo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * Repositório para a entidade EmailsEnviarAnexo
 * Substitui o EmailsEnviarAnexoDao do projeto antigo
 */
@Repository
public interface EmailsEnviarAnexoRepository extends JpaRepository<EmailsEnviarAnexo, Long> {

    /**
     * Busca anexos por sequência do email (equivalente ao método buscaAnexos do DAO antigo)
     * @param sequencia Sequência do email
     * @return Lista de anexos do email
     */
    @Query("SELECT a FROM EmailsEnviarAnexo a WHERE a.sequencia = :sequencia ORDER BY a.ordem")
    List<EmailsEnviarAnexo> findBySequenciaOrderByOrdem(@Param("sequencia") BigDecimal sequencia);

    /**
     * Remove todos os anexos de um email específico
     * @param sequencia Sequência do email
     */
    @Modifying
    @Query("DELETE FROM EmailsEnviarAnexo a WHERE a.sequencia = :sequencia")
    void deleteBySequencia(@Param("sequencia") BigDecimal sequencia);

    /**
     * Conta anexos por sequência
     * @param sequencia Sequência do email
     * @return Quantidade de anexos
     */
    @Query("SELECT COUNT(a) FROM EmailsEnviarAnexo a WHERE a.sequencia = :sequencia")
    long countBySequencia(@Param("sequencia") BigDecimal sequencia);

    /**
     * Busca anexos que existem fisicamente no sistema de arquivos
     * @param sequencia Sequência do email
     * @return Lista de anexos válidos
     */
    List<EmailsEnviarAnexo> findBySequencia(BigDecimal sequencia);
}
