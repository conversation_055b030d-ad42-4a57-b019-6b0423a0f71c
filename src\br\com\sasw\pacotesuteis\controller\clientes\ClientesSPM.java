/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.controller.clientes;

import Dados.Persistencia;
import SasBeans.Clientes;
import SasBeans.ClientesEmail;
import SasBeans.EmailsEnviar;
import SasBeans.Filiais;
import SasBeans.Municipios;
import SasDaos.ClientesDao;
import SasDaos.ClientesEmailDao;
import SasDaos.EmailsEnviarDao;
import SasDaos.FiliaisDao;
import SasDaos.MunicipiosDao;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ClientesSPM {

    /**
     * Contagem do cadastro de clientes
     *
     * @param filtros - filtros para pesquisa
     * @param codPessoa - codigo de pessoa do usuário
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesdao = new ClientesDao();
            return clientesdao.TotalClientesMobWeb(filtros, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>" + e.getMessage());
        }
    }

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> listaClientes(String codfil, String codigo, String nome, String nred, String agencia, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno;
            ClientesDao clientesdao = new ClientesDao();
            if (!"".equals(codigo)) { //codigo
                retorno = clientesdao.ListaClienteCodigo(codfil, codigo, persistencia);
            } else if (!"".equals(nome)) {//nome (razao social)
                retorno = clientesdao.ListaClienteNome(codfil, nome, persistencia);
            } else if (!"".equals(nred)) { //nred
                retorno = clientesdao.ListaClienteNred(codfil, nred, persistencia);
            } else if (!"".equals(agencia)) {//agencia
                retorno = clientesdao.ListaClienteAgencia(codfil, agencia, persistencia);
            } else { //todos do banco
                retorno = clientesdao.ListaCliente(codfil, persistencia);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>" + e.getMessage());
        }
    }

    public List<EmailsEnviar> listarEmails(String codFil, String codCli, String data1, String data2, String parametro, Persistencia persistencia) throws Exception {
        try {
            EmailsEnviarDao emailsEnviarDao = new EmailsEnviarDao();
            List<EmailsEnviar> retorno = emailsEnviarDao.emailsCliente(codCli, codFil, data1, data2, parametro, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>" + e.getMessage());
        }
    }

    public void enviarEmail(EmailsEnviar email, Persistencia persistencia) throws Exception {
        try {
            EmailsEnviarDao emailsEnviarDao = new EmailsEnviarDao();
            emailsEnviarDao.reEnviarEmail(email, persistencia);
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista os municípios com base na query
     *
     * <AUTHOR>
     * @param query - string com a busca
     * @param persistencia - conexão ao banco de dados
     * @return - lista de municipios
     * @throws Exception
     */
    public List<Municipios> listaMunicipios(String query, Persistencia persistencia) throws Exception {
        try {
            MunicipiosDao municipiosdao = new MunicipiosDao();
            return municipiosdao.BuscarMunicipio(query, persistencia);
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista os municípios com base no estado e cidade
     *
     * <AUTHOR>
     * @param estado
     * @param cidade
     * @param persistencia - conexão ao banco de dados
     * @return - lista de municipios
     * @throws Exception
     */
    public List<Municipios> listaMunicipios(String estado, String cidade, Persistencia persistencia) throws Exception {
        try {
            MunicipiosDao municipiosdao = new MunicipiosDao();
            return municipiosdao.ValidarMunicipio(estado, cidade, persistencia);
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Efetua a inserção de cliente, inseri um código automaticamente iniciando
     * em 601 0 001
     *
     * @param clientes - objeto cliente
     * @param persistencia - conexão ao banco de dados
     * @return - retorna o mesmo objeto já com o código do cliente que foi
     * atribuido
     * @throws Exception - pode gerar exception contendo Concorrencia, quando
     * essa exception for gerada, deve-se apresentar um erro em tela e chamar o
     * comando novamente
     */
    public Clientes gravaNovoCliente(Clientes clientes, Persistencia persistencia) throws Exception {
        try {
            int contador = 1;
            Clientes cliente = clientes;
            ClientesDao clientesdao = new ClientesDao();
            String codcli;

            while (contador <= 30) {
                try {
                    codcli = clientesdao.getCodCliMobile(cliente.getCodFil(), persistencia);
                    cliente.setBanco(codcli.substring(0, 3));
                    cliente.setTpCli(codcli.substring(3, 4));
                    cliente.setCodCli(codcli.substring(4, 7));
                    cliente.setCodigo(codcli);
                    cliente.setAgencia(codcli.substring(3, 7));
                    cliente = (Clientes) FuncoesString.removeAcentoObjeto(cliente);
                    clientesdao.inserir(cliente, persistencia);
                    return cliente;
                } catch (Exception ex) {
                } finally {
                    contador++;
                }
            }
            throw new Exception("ClientesSPM.concorrencia");
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>" + e.getMessage());
        }
    }

    public Boolean existeEmail(ClientesEmail cliEmail, Persistencia persistencia) throws Exception {
        try {
            ClientesEmailDao cliEmailDao = new ClientesEmailDao();
            if (!cliEmailDao.existeEmail(cliEmail, persistencia)) {
                return Boolean.FALSE;
            } else {
                return Boolean.TRUE;
            }

        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message> " + e.getMessage());
        }
    }

    public void cadastrarEmail(ClientesEmail cliEmail, Persistencia persistencia) throws Exception {
        try {
            ClientesEmailDao cliEmailDao = new ClientesEmailDao();
            cliEmailDao.InserirEmail(cliEmail, persistencia);

        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message> " + e.getMessage());
        }
    }

    public List<ClientesEmail> buscarEmail(String query, Persistencia persistencia) throws Exception {
        try {
            ClientesEmailDao clientesEmailDao = new ClientesEmailDao();
            return clientesEmailDao.selecionarEmail(query, persistencia);
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>" + e.getMessage());
        }
    }

    public void deletarEmail(ClientesEmail clientesEmail, Persistencia persistencia) throws Exception {
        try {
            ClientesEmailDao clientesEmailDao = new ClientesEmailDao();
            clientesEmailDao.deletarEmail(clientesEmail, persistencia);
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Grava as alterações no cadastro de clientes
     *
     * @param cliente
     * @param persistencia
     * @throws Exception
     */
    public void gravaCliente(Clientes cliente, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesdao = new ClientesDao();
            cliente = (Clientes) FuncoesString.removeAcentoObjeto(cliente);
            clientesdao.GravaClienteMobile(cliente, persistencia);
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>");

        }
    }

    /**
     * Efetua a inserção de cliente, inseri um código automaticamente iniciando
     * em 601 0 001
     *
     * @param clientes - objeto cliente
     * @param persistencia - conexão ao banco de dados
     * @throws Exception - pode gerar exception contendo Concorrencia, quando
     * essa exception for gerada, deve-se apresentar um erro em tela e chamar o
     * comando novamente
     */
    public void importarClientes(List<Clientes> clientes, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesdao = new ClientesDao();
            for (Clientes cliente : clientes) {
                try {
                    cliente.setCodCli(clientesdao.getCodCliImportacaoSPM(cliente.getCodFil().toPlainString(), cliente.getBanco(), persistencia));
                    cliente.setCodigo(cliente.getBanco() + cliente.getTpCli() + cliente.getCodCli());
                    clientesdao.inserir(cliente, persistencia);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        } catch (Exception e) {
            throw new Exception("ClientesSPM.falhageral<message>" + e.getMessage());
        }
    }
}
