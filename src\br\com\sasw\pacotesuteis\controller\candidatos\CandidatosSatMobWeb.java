/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.controller.candidatos;

import Dados.Persistencia;
import SasBeans.Municipios;
import SasBeans.Pe_Doctos;
import SasDaos.MunicipiosDao;
import SasDaos.Pe_DoctosDao;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CandidatosSatMobWeb {

    /**
     * Lista os municípios com base na query
     *
     * <AUTHOR>
     * @param query - string com a busca
     * @param persistencia - conexão ao banco de dados
     * @return - lista de municipios
     * @throws Exception
     */
    public List<Municipios> listarMunicipios(String query, Persistencia persistencia) throws Exception {
        try {
            MunicipiosDao municipiosdao = new MunicipiosDao();
            return municipiosdao.BuscarMunicipio(query, persistencia);
        } catch (Exception e) {
            throw new Exception("cliente.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista os municípios com base no estado e cidade
     *
     * <AUTHOR>
     * @param estado
     * @param cidade
     * @param persistencia - conexão ao banco de dados
     * @return - lista de municipios
     * @throws Exception
     */
    public List<Municipios> listarMunicipios(String estado, String cidade, Persistencia persistencia) throws Exception {
        try {
            MunicipiosDao municipiosdao = new MunicipiosDao();
            return municipiosdao.ValidarMunicipio(estado, cidade, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista todos os documentos de uma pessoa pelo seu código
     *
     * @param codPessoa
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Pe_Doctos> listarDoctos(BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            Pe_DoctosDao pe_DoctosDao = new Pe_DoctosDao();
            return pe_DoctosDao.listarDoctos(codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Insere um documento verificando a existencia antes, e em caso positivo,
     * atualiza o arquivo em vez de inserir.
     *
     * @param pe_Doctos
     * @param persistencia
     * @return retorna o arquivo com a ordem
     * @throws Exception
     */
    public Pe_Doctos inserirDocumento(Pe_Doctos pe_Doctos, Persistencia persistencia) throws Exception {
        try {
            Pe_DoctosDao pe_DoctosDao = new Pe_DoctosDao();
            String ordem = pe_DoctosDao.existeArquivo(pe_Doctos.getCodigo(), pe_Doctos.getDescricao(), persistencia);
            if (null == ordem) {
                ordem = pe_DoctosDao.maxOrdem(pe_Doctos.getCodigo(), persistencia);
                pe_Doctos.setOrdem(ordem);
                pe_DoctosDao.inserirDocumento(pe_Doctos, persistencia);
            } else {
                pe_Doctos.setOrdem(ordem);
                pe_DoctosDao.atualizaDocumentos(pe_Doctos, persistencia);
            }
            return pe_Doctos;
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Exclui um documento do banco de dados
     *
     * @param pe_Doctos
     * @param persistencia
     * @throws Exception
     */
    public void deletarDocumento(Pe_Doctos pe_Doctos, Persistencia persistencia) throws Exception {
        try {
            Pe_DoctosDao pe_DoctosDao = new Pe_DoctosDao();
            pe_DoctosDao.excluirDocumento(pe_Doctos, persistencia);
        } catch (Exception e) {
            throw new Exception("pessoa.falhageral<message>" + e.getMessage());
        }
    }
}
