﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtRemun/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtRemun/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtRemun">
                    <xs:annotation>
                        <xs:documentation>Remuneracao de trabalhador vinculado ao RGPS</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do evento</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="indRetif">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Indicativo de Retificacao</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nrRecibo" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Numero do recibo do arquivo a ser retificado</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="1"/>
                                                    <xs:maxLength value="40"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="indApuracao">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Indicativo de periodo de apuracao</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="perApur">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Periodo ao qual se referem as informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="tpAmb">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Tipo de ambiente</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="procEmi">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Processo de emissao do evento</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="verProc">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="1"/>
                                                    <xs:maxLength value="20"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideTrabalhador">
                                <xs:annotation>
                                    <xs:documentation>Identificacao do Trabalhador</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="cpfTrab">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>CPF do trabalhador</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:length value="11"/>
                                                    <xs:pattern value="\d{11}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nisTrab" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>NIS</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:maxLength value="11"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="infoMV" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacao de Multiplos Vinculos</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="indMV">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Indicador de MV</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="remunOutrEmpr" type="TRemunOutrasEmpresas" maxOccurs="999">
                                                        <xs:annotation>
                                                            <xs:documentation>Remuneracao recebida pelo trabalhador em outras empresas ou atividades</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="infoComplem" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes complementares de identificacao do trabalhador</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="nmTrab">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Nome do Trabalhador</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="2"/>
                                                                <xs:maxLength value="70"/>
                                                                <xs:whiteSpace value="preserve"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="dtNascto">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Data de Nascimento</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:date">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="sucessaoVinc" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Grupo de informacoes da sucessao de vinculo trabalhista</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="tpInscAnt">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Tipo de inscricao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="cnpjEmpregAnt">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>CNPJ do empregador anterior</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:pattern value="\d{8,14}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="matricAnt" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Matricula no empregador anterior</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="30"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dtAdm">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Data de admissao do trabalhador</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:date">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="observacao" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Observacao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="2"/>
                                                                            <xs:maxLength value="255"/>
                                                                            <xs:whiteSpace value="preserve"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="procJudTrab" minOccurs="0" maxOccurs="99">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes sobre a existencia de processos judiciais do trabalhador</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="tpTrib">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Abrangencia pela decisao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="nrProcJud">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Nr Processo Judicial</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:length value="20"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="codSusp" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo do Indicativo da Suspensao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:integer">
                                                                <xs:pattern value="\d{1,14}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="infoInterm" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes relativas ao trabalho intermitente</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="qtdDiasInterm">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Quantidade de dias trabalhados - contrato intermitente</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d{1,2}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="dmDev" maxOccurs="99">
                                <xs:annotation>
                                    <xs:documentation>Demonstrativos de valores devidos ao trabalhador</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="ideDmDev">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Identificador do demonstrativo de pagamento</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="1"/>
                                                    <xs:maxLength value="30"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="codCateg">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>COdigo da Categoria do Trabalhador</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:integer">
                                                    <xs:pattern value="\d{3}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="infoPerApur" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Remuneracao no periodo de apuracao</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="ideEstabLot" maxOccurs="500">
                                                        <xs:annotation>
                                                            <xs:documentation>Identificacao do estabelecimento e lotacao</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="tpInsc">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="nrInsc">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Numero de Inscricao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:pattern value="\d{8,14}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="codLotacao">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo da Lotacao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="30"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="qtdDiasAv" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Quantidade de dias trabalhados pelo avulso</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d{1,2}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="remunPerApur" maxOccurs="8">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Remuneracao do Trabalhador</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="matricula" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Matricula</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="30"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="indSimples" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Indicador de Contribuicao Substituida</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:byte">
                                                                                        <xs:pattern value="\d"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="itensRemun" type="TItemRemuneracao" maxOccurs="200">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Itens da Remuneracao do Trabalhador</xs:documentation>
                                                                                </xs:annotation>
                                                                            </xs:element>
                                                                            <xs:element name="infoSaudeColet" type="TSaudeCol" minOccurs="0">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Informacoes de plano privado coletivo empresarial de assistencia à saude</xs:documentation>
                                                                                </xs:annotation>
                                                                            </xs:element>
                                                                            <xs:element name="infoAgNocivo" minOccurs="0">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Grau de Exposicao a Agentes Nocivos</xs:documentation>
                                                                                </xs:annotation>
                                                                                <xs:complexType>
                                                                                    <xs:sequence>
                                                                                        <xs:element name="grauExp">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Grau de exposicao a agentes nocivos, conforme tabela 2</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:byte">
                                                                                                    <xs:pattern value="\d"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                    </xs:sequence>
                                                                                </xs:complexType>
                                                                            </xs:element>
                                                                            <xs:element name="infoTrabInterm" minOccurs="0" maxOccurs="99">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Informacoes da(s) convocacao(oes) de trabalho intermitente</xs:documentation>
                                                                                </xs:annotation>
                                                                                <xs:complexType>
                                                                                    <xs:sequence>
                                                                                        <xs:element name="codConv">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>COdigo que identifica a convocacao para trabalho intermitente</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:string">
                                                                                                    <xs:minLength value="2"/>
                                                                                                    <xs:maxLength value="30"/>
                                                                                                    <xs:whiteSpace value="preserve"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                    </xs:sequence>
                                                                                </xs:complexType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="infoPerAnt" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Remuneracao relativa a diferencas salariais</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="ideADC" maxOccurs="8">
                                                        <xs:annotation>
                                                            <xs:documentation>Instrumento ou situacao ensejadora da remuneracao em Periodos Anteriores</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="dtAcConv" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Data da assinatura do acordo/convencao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:date">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="tpAcConv">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Tipo do instrumento ou situacao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="1"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="compAcConv" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Competencia</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dtEfAcConv" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Data dos efeitos do acordo/convencao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:date">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dsc">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Descricao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="2"/>
                                                                            <xs:maxLength value="255"/>
                                                                            <xs:whiteSpace value="preserve"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="remunSuc">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Indicativo de verbas devidas p/ sucessora a desligados na sucedida</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:pattern value="[N|S]"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="idePeriodo" maxOccurs="180">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Identificacao do periodo de referencia da remuneracao</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="perRef">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Periodo de referencia</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="ideEstabLot" maxOccurs="500">
                                                                                <xs:annotation>
                                                                                    <xs:documentation>Identificacao do estabelecimento e lotacao</xs:documentation>
                                                                                </xs:annotation>
                                                                                <xs:complexType>
                                                                                    <xs:sequence>
                                                                                        <xs:element name="tpInsc">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:byte">
                                                                                                    <xs:pattern value="\d"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="nrInsc">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Numero de Inscricao</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:string">
                                                                                                    <xs:pattern value="\d{8,14}"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="codLotacao">
                                                                                            <xs:simpleType>
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>COdigo da Lotacao</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:restriction base="xs:string">
                                                                                                    <xs:minLength value="1"/>
                                                                                                    <xs:maxLength value="30"/>
                                                                                                </xs:restriction>
                                                                                            </xs:simpleType>
                                                                                        </xs:element>
                                                                                        <xs:element name="remunPerAnt" maxOccurs="8">
                                                                                            <xs:annotation>
                                                                                                <xs:documentation>Remuneracao do Trabalhador</xs:documentation>
                                                                                            </xs:annotation>
                                                                                            <xs:complexType>
                                                                                                <xs:sequence>
                                                                                                    <xs:element name="matricula" minOccurs="0">
                                                                                                        <xs:simpleType>
                                                                                                            <xs:annotation>
                                                                                                                <xs:documentation>Matricula</xs:documentation>
                                                                                                            </xs:annotation>
                                                                                                            <xs:restriction base="xs:string">
                                                                                                                <xs:minLength value="1"/>
                                                                                                                <xs:maxLength value="30"/>
                                                                                                            </xs:restriction>
                                                                                                        </xs:simpleType>
                                                                                                    </xs:element>
                                                                                                    <xs:element name="indSimples" minOccurs="0">
                                                                                                        <xs:simpleType>
                                                                                                            <xs:annotation>
                                                                                                                <xs:documentation>Indicador de Contribuicao Substituida</xs:documentation>
                                                                                                            </xs:annotation>
                                                                                                            <xs:restriction base="xs:byte">
                                                                                                                <xs:pattern value="\d"/>
                                                                                                            </xs:restriction>
                                                                                                        </xs:simpleType>
                                                                                                    </xs:element>
                                                                                                    <xs:element name="itensRemun" type="TItemRemuneracao" maxOccurs="200">
                                                                                                        <xs:annotation>
                                                                                                            <xs:documentation>Itens da Remuneracao do Trabalhador</xs:documentation>
                                                                                                        </xs:annotation>
                                                                                                    </xs:element>
                                                                                                    <xs:element name="infoAgNocivo" minOccurs="0">
                                                                                                        <xs:annotation>
                                                                                                            <xs:documentation>Grau de Exposicao a Agentes Nocivos</xs:documentation>
                                                                                                        </xs:annotation>
                                                                                                        <xs:complexType>
                                                                                                            <xs:sequence>
                                                                                                                <xs:element name="grauExp">
                                                                                                                    <xs:simpleType>
                                                                                                                        <xs:annotation>
                                                                                                                            <xs:documentation>Grau de exposicao a agentes nocivos, conforme tabela 2</xs:documentation>
                                                                                                                        </xs:annotation>
                                                                                                                        <xs:restriction base="xs:byte">
                                                                                                                            <xs:pattern value="\d"/>
                                                                                                                        </xs:restriction>
                                                                                                                    </xs:simpleType>
                                                                                                                </xs:element>
                                                                                                            </xs:sequence>
                                                                                                        </xs:complexType>
                                                                                                    </xs:element>
                                                                                                    <xs:element name="infoTrabInterm" minOccurs="0" maxOccurs="99">
                                                                                                        <xs:annotation>
                                                                                                            <xs:documentation>Informacoes da(s) convocacao(oes) de trabalho intermitente</xs:documentation>
                                                                                                        </xs:annotation>
                                                                                                        <xs:complexType>
                                                                                                            <xs:sequence>
                                                                                                                <xs:element name="codConv">
                                                                                                                    <xs:simpleType>
                                                                                                                        <xs:annotation>
                                                                                                                            <xs:documentation>COdigo que identifica a convocacao para trabalho intermitente</xs:documentation>
                                                                                                                        </xs:annotation>
                                                                                                                        <xs:restriction base="xs:string">
                                                                                                                            <xs:minLength value="2"/>
                                                                                                                            <xs:maxLength value="30"/>
                                                                                                                            <xs:whiteSpace value="preserve"/>
                                                                                                                        </xs:restriction>
                                                                                                                    </xs:simpleType>
                                                                                                                </xs:element>
                                                                                                            </xs:sequence>
                                                                                                        </xs:complexType>
                                                                                                    </xs:element>
                                                                                                </xs:sequence>
                                                                                            </xs:complexType>
                                                                                        </xs:element>
                                                                                    </xs:sequence>
                                                                                </xs:complexType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="infoComplCont" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes complementares contratuais do trabalhador</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="codCBO">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>CBO</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="\d{4,6}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="natAtividade" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Natureza da atividade</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="qtdDiasTrab" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Qtd dias trabalhados</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d{1,2}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Numero de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TRemunOutrasEmpresas">
        <xs:annotation>
            <xs:documentation>Remuneracao em outras empresas ou atividades</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Numero de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="codCateg">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo da Categoria</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:integer">
                        <xs:pattern value="\d{3}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="vlrRemunOE">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Valor da remuneracao recebida pelo trabalhador</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="14"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="999999999999"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TItemRemuneracao">
        <xs:annotation>
            <xs:documentation>Informacoes dos Itens da remuneracao</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="codRubr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo da Rubrica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="30"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="ideTabRubr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Identificador da tabela de rubricas</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="8"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="qtdRubr" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Qtde de referencia para apuracao da rubrica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="6"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="9999.99"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fatorRubr" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Fator utilizado na apuracao da rubrica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="5"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="999.99"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="vrUnit" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Valor Unitario</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="14"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="999999999999"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="vrRubr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Valor total da rubrica</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="14"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="999999999999"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TSaudeCol">
        <xs:annotation>
            <xs:documentation>Planos de saude coletivo</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="detOper" maxOccurs="99">
                <xs:annotation>
                    <xs:documentation>Detalhamento dos valores pagos a Operadoras de Planos de Saude</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="cnpjOper">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>CNPJ de Operadora do Plano de Saude</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:length value="14"/>
                                    <xs:pattern value="\d{14}"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="regANS">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Registro da operadora na Agencia Nacional de Saude</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:string">
                                    <xs:length value="6"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="vrPgTit">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Valor pago pelo Titular</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="14"/>
                                    <xs:fractionDigits value="2"/>
                                    <xs:maxInclusive value="999999999999"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="detPlano" minOccurs="0" maxOccurs="99">
                            <xs:annotation>
                                <xs:documentation>Informacoes do dependente do plano privado de saude</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="tpDep">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Tipo de dependente</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:length value="2"/>
                                                <xs:pattern value="\d{2}"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="cpfDep" minOccurs="0">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Numero de Inscricao no CPF</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:pattern value="\d{11}"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="nmDep">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Nome do Dependente</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:minLength value="2"/>
                                                <xs:maxLength value="70"/>
                                                <xs:whiteSpace value="preserve"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="dtNascto">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Data de Nascimento</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:date">
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="vlrPgDep">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Valor pago relativo ao dependente</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:decimal">
                                                <xs:totalDigits value="14"/>
                                                <xs:fractionDigits value="2"/>
                                                <xs:maxInclusive value="999999999999"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
