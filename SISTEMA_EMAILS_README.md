# Sistema de Emails - Migração do SatelliteServer

Este documento descreve a migração do sistema de envio de emails do projeto SatelliteServer para uma arquitetura moderna usando Spring Boot.

## 📋 Visão Geral

O sistema antigo utilizava uma Thread manual com interface Swing para processar emails. A nova implementação usa:

- **Spring Boot** com arquitetura moderna
- **@Scheduled** para processamento automático
- **Spring Mail** para envio SMTP
- **JPA/Hibernate** para acesso aos dados
- **REST API** para monitoramento e controle

## 🏗️ Arquitetura

### Componentes Principais

1. **EmailsEnviar** - Entidade JPA mapeando a tabela de emails
2. **EmailsEnviarAnexo** - Entidade JPA para anexos
3. **EmailService** - Serviço principal de envio
4. **EmailSchedulerService** - Scheduler automático
5. **EmailController** - API REST para controle
6. **EmailConfig** - Configurações externalizadas

### Fluxo de Processamento

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Scheduler     │───▶│   EmailService   │───▶│   Spring Mail   │
│   (30s)         │    │                  │    │   (SMTP)        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                       │
         ▼                        ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Database      │    │   File System    │    │   Email Server  │
│   (Emails)      │    │   (Anexos)       │    │   (SMTP)        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🚀 Como Usar

### 1. Configuração Inicial

Ajuste as configurações no `application.properties`:

```properties
# Banco de dados
spring.datasource.url=**********************************************************
spring.datasource.username=seu_usuario
spring.datasource.password=sua_senha

# Configurações de email
email.default-smtp=smtplw.com.br
email.default-username=sasw
email.default-password=xNiadJEj9607
```

### 2. Executar a Aplicação

```bash
# Compilar
mvn clean compile

# Executar
mvn spring-boot:run
```

### 3. Monitoramento via API

#### Status do Sistema
```bash
GET http://localhost:8081/esocial-novo/api/emails/status
```

#### Iniciar/Parar Processamento
```bash
POST http://localhost:8081/esocial-novo/api/emails/iniciar
POST http://localhost:8081/esocial-novo/api/emails/parar
```

#### Processar Emails Imediatamente
```bash
POST http://localhost:8081/esocial-novo/api/emails/processar-agora
```

#### Estatísticas
```bash
GET http://localhost:8081/esocial-novo/api/emails/estatisticas
```

## 📊 Endpoints da API

| Método | Endpoint | Descrição |
|--------|----------|-----------|
| GET | `/api/emails/status` | Status do scheduler |
| POST | `/api/emails/iniciar` | Inicia processamento |
| POST | `/api/emails/parar` | Para processamento |
| POST | `/api/emails/processar-agora` | Força processamento |
| GET | `/api/emails/pendentes` | Lista emails pendentes |
| GET | `/api/emails/enviados-hoje` | Emails enviados hoje |
| GET | `/api/emails/com-falha` | Emails com erro |
| GET | `/api/emails/estatisticas` | Estatísticas gerais |
| GET | `/api/emails/health` | Health check |

## 🔄 Migração do Sistema Antigo

### Diferenças Principais

| Aspecto | Sistema Antigo | Sistema Novo |
|---------|----------------|--------------|
| **Interface** | Swing Desktop | REST API |
| **Threading** | Thread manual | @Scheduled |
| **Configuração** | Hardcoded | Properties |
| **Banco** | JDBC direto | JPA/Hibernate |
| **Email** | Apache Commons | Spring Mail |
| **Monitoramento** | Interface gráfica | Endpoints REST |

### Compatibilidade

✅ **Mantido:**
- Estrutura das tabelas `EmailsEnviar` e `EmailsEnviarAnexo`
- Lógica de processamento de destinatários
- Suporte a anexos
- Logs em arquivo
- Configurações SMTP específicas por email

✅ **Melhorado:**
- Configurações externalizadas
- Tratamento de erros robusto
- Monitoramento via API
- Logs estruturados
- Performance otimizada

## 🛠️ Configurações Avançadas

### Scheduler

```properties
# Intervalo de processamento (ms)
email.scheduler-interval-ms=30000

# Habilitar/desabilitar
email.scheduler-enabled=true

# Processamento em lotes
email.batch-size=10
email.process-in-batches=true
```

### Anexos

```properties
# Caminho base dos anexos
email.anexo-base-path=C:\\xampp\\htdocs\\satmobile\\documentos\\anexo-email\\

# Tamanho máximo (MB)
email.max-anexo-size-mb=25
```

### Logs

```properties
# Caminho base dos logs
email.log-base-path=C:\\SatelliteServer\\log\\

# Habilitar logs em arquivo
email.log-emails-enabled=true
```

## 🔍 Troubleshooting

### Problemas Comuns

1. **Emails não são processados**
   - Verifique se o scheduler está ativo: `GET /api/emails/status`
   - Verifique logs da aplicação

2. **Erro de conexão SMTP**
   - Confirme configurações no `application.properties`
   - Teste conectividade com o servidor SMTP

3. **Anexos não encontrados**
   - Verifique o caminho configurado em `email.anexo-base-path`
   - Confirme permissões de leitura

4. **Erro de banco de dados**
   - Verifique string de conexão
   - Confirme se as tabelas existem

### Logs Importantes

```bash
# Logs da aplicação
tail -f logs/spring.log

# Logs de email (compatibilidade)
tail -f C:\SatelliteServer\log\2024-01-01\log_processamento_emails.txt
```

## 📈 Monitoramento

### Health Check

```bash
GET http://localhost:8081/esocial-novo/api/emails/health
```

### Métricas (Actuator)

```bash
GET http://localhost:8081/esocial-novo/actuator/health
GET http://localhost:8081/esocial-novo/actuator/metrics
GET http://localhost:8081/esocial-novo/actuator/scheduledtasks
```

## 🔧 Desenvolvimento

### Executar em Modo Debug

```properties
# application.properties
email.scheduler-enabled=false
logging.level.com.br.sasw.esocial_novo=DEBUG
spring.jpa.show-sql=true
```

### Testes

```bash
# Executar testes
mvn test

# Processar emails manualmente
POST http://localhost:8081/esocial-novo/api/emails/processar-agora
```

## 📝 Notas de Implementação

1. **Thread Safety**: O scheduler usa `AtomicBoolean` para evitar execuções concorrentes
2. **Transações**: Operações de banco são transacionais
3. **Retry**: Sistema de retry configurável para falhas temporárias
4. **Performance**: Processamento em lotes para melhor performance
5. **Compatibilidade**: Mantém compatibilidade com logs e estrutura existente

## 🎯 Próximos Passos

- [ ] Implementar dashboard web para monitoramento
- [ ] Adicionar métricas customizadas
- [ ] Implementar notificações de falha
- [ ] Otimizar queries de banco
- [ ] Adicionar testes de integração
