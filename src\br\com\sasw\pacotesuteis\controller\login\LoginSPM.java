/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.controller.login;

import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeans.Pessoa;
import SasBeans.PessoaCliAut;
import SasBeans.PessoaLogin;
import SasBeans.SasPWFill;
import SasDaos.AcessosDao;
import SasDaos.FiliaisDao;
import SasDaos.PessoaCliAutDao;
import SasDaos.PessoaDao;
import SasDaos.PessoaLoginDao;
import SasDaos.SasPwFilDao;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LoginSPM {

    /**
     * Valida o Login do Usuário
     *
     * @param Email - Email de login
     * @param PWWeb - Senha de Acesso
     * @param persistencia - Conexão ao banco de dados
     * @return - Lista de empresas que pode acessar
     * @throws Exception
     */
    public List<PessoaLogin> logar(String Email, String PWWeb, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            PessoaLoginDao pessoalogindao = new PessoaLoginDao();
            List<PessoaLogin> retorno = new ArrayList();
            boolean senhacerta = false;
            List<Pessoa> lpessoa = pessoadao.BuscaEmail(Email, persistencia);
            if (lpessoa.size() <= 0) {
                throw new Exception("login.usuarioerrado");
            }
            for (Pessoa p : lpessoa) {
                if (PWWeb.equals(p.getPWWeb())) {
                    senhacerta = true;
                    retorno = pessoalogindao.getPessoaLogin(p.getCodigo(), persistencia);
                }
            }
            if (!senhacerta) {
                throw new Exception("login.senhaerrada");
            } else if (retorno.size() <= 0) {
                throw new Exception("login.usuariosemacessocadastrado");
            } else {
                return retorno;
            }
        } catch (Exception e) {
            throw new Exception("loginSPM.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca os dados do usuário
     *
     * @param CodPessoa - Código da pessoa
     * @param persistencia - conexão ao banco de dados (parametro SATELLITE)
     * @return - Dados: codigo, nome, email
     * @throws Exception
     */
    public Pessoa buscaPessoa(BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            Pessoa pessoa = pessoadao.getPessoaCodigo(CodPessoa, persistencia);
            return pessoa;
        } catch (Exception e) {
            throw new Exception("loginSPM.falhageral<message>" + e.getMessage());
        }
    }

    public SasPWFill buscarFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            return saspwfildao.buscaSasPWFill(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("loginSPM.falhageral<message>" + e.getMessage());
        }
    }

    public String situacaoUsuario(String email, Persistencia persistencia) throws Exception {
        try {
            AcessosDao acesso = new AcessosDao();
            return acesso.situacaoUsuario(email, persistencia);
        } catch (Exception e) {
            throw new Exception("loginSPM.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista as filiais autorizadas no cliente
     *
     * @param CodPessoa - Código da pessoa
     * @param persistencia - conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<SasPWFill> selecionaEmpresa(BigDecimal CodPessoa, Persistencia persistencia) throws Exception {
        try {
            SasPwFilDao saspwfildao = new SasPwFilDao();
            List<SasPWFill> lsaspwfill = saspwfildao.getSasPWFill(CodPessoa, persistencia);
            return lsaspwfill;
        } catch (Exception e) {
            throw new Exception("loginSPM.falhageral<message>" + e.getMessage());
        }
    }

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("loginSPM.falhageral<message>" + e.getMessage());
        }
    }

    public List<PessoaCliAut> listarClientes(PessoaCliAut cliente, Boolean excluidos, Persistencia persistencia) throws Exception {
        try {
            PessoaCliAutDao pessoacliautdao = new PessoaCliAutDao();
            return pessoacliautdao.listarClientes(cliente, excluidos, persistencia);
        } catch (Exception e) {
            throw new Exception("loginSPM.falhageral<message>" + e.getMessage());
        }
    }
}
