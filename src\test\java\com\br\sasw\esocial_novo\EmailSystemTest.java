package com.br.sasw.esocial_novo;

import com.br.sasw.esocial_novo.entity.EmailsEnviar;
import com.br.sasw.esocial_novo.repository.EmailsEnviarRepository;
import com.br.sasw.esocial_novo.service.EmailSchedulerService;
import com.br.sasw.esocial_novo.service.EmailService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Teste de integração para o sistema de emails
 * Demonstra como usar e testar o sistema migrado
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class EmailSystemTest {

    @Autowired
    private EmailService emailService;

    @Autowired
    private EmailSchedulerService schedulerService;

    @Autowired
    private EmailsEnviarRepository emailRepository;

    /**
     * Teste básico de inserção e processamento de email
     */
    @Test
    public void testInserirEProcessarEmail() {
        // Criar um email de teste
        EmailsEnviar email = new EmailsEnviar();
        email.setSequencia(new BigDecimal("999999"));
        email.setSmtp("smtplw.com.br");
        email.setDestEmail("<EMAIL>");
        email.setDestNome("Teste");
        email.setRemetEmail("<EMAIL>");
        email.setRemetNome("SASw");
        email.setAssunto("Teste de Email - Sistema Migrado");
        email.setMensagem("<html><body><h1>Teste</h1><p>Este é um email de teste do sistema migrado.</p></body></html>");
        email.setAutLogin("sasw");
        email.setAutSenha("xNiadJEj9607");
        email.setPorta(587);
        email.setFlagEnviado(""); // Pendente
        email.setDtInclusao(LocalDate.now());
        email.setHrInclusao(LocalTime.now());
        email.setCodFil("01");
        email.setCodCli("001");
        email.setParametro("TESTE");

        // Salvar no banco
        emailRepository.save(email);

        // Verificar se foi salvo
        assert emailRepository.findById(email.getSequencia()).isPresent();

        // Processar emails pendentes
        emailService.processarEmailsPendentes();

        // Verificar estatísticas
        EmailService.EmailStats stats = emailService.getEmailStats();
        System.out.println("Emails pendentes: " + stats.pendentes());
        System.out.println("Emails enviados hoje: " + stats.enviadosHoje());
        System.out.println("Emails com falha: " + stats.comFalha());
    }

    /**
     * Teste do scheduler
     */
    @Test
    public void testScheduler() {
        // Verificar status inicial
        boolean ativo = schedulerService.isSchedulerAtivo();
        System.out.println("Scheduler ativo: " + ativo);

        // Parar scheduler
        schedulerService.pararScheduler();
        assert !schedulerService.isSchedulerAtivo();

        // Iniciar scheduler
        schedulerService.iniciarScheduler();
        assert schedulerService.isSchedulerAtivo();

        // Processar imediatamente
        try {
            schedulerService.processarEmailsImediato();
            System.out.println("Processamento manual executado com sucesso");
        } catch (Exception e) {
            System.out.println("Erro no processamento manual: " + e.getMessage());
        }
    }

    /**
     * Teste de consultas do repositório
     */
    @Test
    public void testRepositoryQueries() {
        // Buscar emails pendentes
        var emailsPendentes = emailRepository.findEmailsPendentes();
        System.out.println("Emails pendentes encontrados: " + emailsPendentes.size());

        // Buscar emails enviados hoje
        var emailsHoje = emailRepository.findEmailsEnviadosNaData(LocalDate.now());
        System.out.println("Emails enviados hoje: " + emailsHoje.size());

        // Buscar emails com falha
        var emailsComFalha = emailRepository.findEmailsComFalha();
        System.out.println("Emails com falha: " + emailsComFalha.size());

        // Contar emails pendentes
        long totalPendentes = emailRepository.countEmailsPendentes();
        System.out.println("Total de emails pendentes: " + totalPendentes);
    }

    /**
     * Teste de processamento de destinatários
     */
    @Test
    public void testProcessamentoDestinatarios() {
        EmailsEnviar email = new EmailsEnviar();
        
        // Teste com múltiplos destinatários e formatação
        email.setDestEmail("<EMAIL>; <EMAIL> , <EMAIL>;<EMAIL>");
        
        String[] destinatarios = email.getDestinatariosLimpos();
        
        assert destinatarios.length == 4;
        assert "<EMAIL>".equals(destinatarios[0]);
        assert "<EMAIL>".equals(destinatarios[1]);
        assert "<EMAIL>".equals(destinatarios[2]);
        assert "<EMAIL>".equals(destinatarios[3]);
        
        System.out.println("Teste de destinatários passou!");
    }
}
