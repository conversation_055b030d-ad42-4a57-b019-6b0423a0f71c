package Arquivo;

/**
 *
 * <AUTHOR>
 */
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import java.io.*;
import java.nio.charset.StandardCharsets;

public class ArquivoLogs implements GerarLog {

    private String caminho;

    public ArquivoLogs(String caminho) {
        this.caminho = caminho;
    }

    /**
     * Grava uma linha de log para o sistema - usar apenas para log
     *
     * @param conteudo - conteudo que sera gravada
     * @param caminho - nome do arquivo de log
     */
    @Override
    public void gerarLog(String conteudo) {
        try {
            // Gravando no arquivo  
            File arquivo = new File(caminho);
            File diretorio = new File(caminho.substring(0, caminho.lastIndexOf("\\")));
            BufferedWriter writer;
            if (!diretorio.exists()) {
                diretorio.mkdirs(); // cria diretórios caso não estejam criados
            }
            if (!arquivo.exists()) {
                writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(caminho), StandardCharsets.UTF_8));
            } else {
                writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(caminho,true), StandardCharsets.UTF_8));
            }
            String texto = "********** Mensagem - " + getDataAtual("TELA") + " " + getDataAtual("HORASEGUNDOS") + " *********************************************************";
            writer.newLine();
            writer.write(texto);
            writer.newLine();
            writer.write(conteudo);
            writer.newLine();
            writer.close();
        } catch (Exception e) {

        }
    }
}
