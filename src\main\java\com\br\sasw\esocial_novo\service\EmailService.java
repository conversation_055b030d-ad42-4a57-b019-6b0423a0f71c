package com.br.sasw.esocial_novo.service;

import com.br.sasw.esocial_novo.config.EmailConfig;
import com.br.sasw.esocial_novo.entity.EmailsEnviar;
import com.br.sasw.esocial_novo.entity.EmailsEnviarAnexo;
import com.br.sasw.esocial_novo.repository.EmailsEnviarRepository;
import com.br.sasw.esocial_novo.repository.EmailsEnviarAnexoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.mail.internet.MimeMessage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class EmailService {

    private final EmailsEnviarRepository emailRepository;
    private final EmailsEnviarAnexoRepository anexoRepository;
    private final EmailConfig emailConfig;
    private final JavaMailSender defaultMailSender;

    @Value("${email.anexo-base-path}")
    private String basePath;

    @Transactional
    public void processarEmailsPendentes() {
        try {
            List<EmailsEnviar> emailsPendentes = emailRepository.findEmailsPendentes();
            
            if (emailsPendentes.isEmpty()) {
                log.info("Nenhum email pendente para envio");
                return;
            }

            log.info("Processando {} emails pendentes", emailsPendentes.size());
            logToFile("Emails a enviar: " + emailsPendentes.size(), emailConfig.getEmailLogFile());

            for (EmailsEnviar email : emailsPendentes) {
                try {
                    processarEmail(email);
                } catch (Exception e) {
                    log.error("Erro ao processar email sequência {}: {}", email.getSequencia(), e.getMessage(), e);
                    marcarEmailComoFalha(email, e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("Erro geral no processamento de emails: {}", e.getMessage(), e);
        }
    }

    private void processarEmail(EmailsEnviar email) throws Exception {
        if (email.isEnviado()) {
            return;
        }

        List<EmailsEnviarAnexo> anexos = anexoRepository.findBySequenciaOrderByOrdem(email.getSequencia());
        
        String[] destinatarios = email.getDestinatariosLimpos();
        if (destinatarios.length == 0) {
            log.warn("Email sequência {} não possui destinatários válidos", email.getSequencia());
            return;
        }

        if (email.getParametro() != null && !email.getParametro().trim().isEmpty()) {
            enviarEmailMultiplosDestinatarios(email, destinatarios, anexos);
        } else {
            enviarEmailIndividual(email, destinatarios, anexos);
        }

        email.marcarComoEnviado();
        emailRepository.save(email);

        log.info("Email '{}' enviado com sucesso para {}", email.getAssunto(), email.getDestEmail());
        logToFile(email.getAssunto() + " enviado com sucesso para " + email.getDestEmail(), 
                 emailConfig.getEmailLogFile());
    }


    private void enviarEmailMultiplosDestinatarios(EmailsEnviar email, String[] destinatarios, 
                                                  List<EmailsEnviarAnexo> anexos) throws Exception {
        
        log.info("Enviando {} para múltiplos destinatários: {}", email.getAssunto(), String.join(", ", destinatarios));
        logToFile("Enviando " + email.getAssunto() + " para " + destinatarios[0], emailConfig.getEmailLogFile());

        JavaMailSender mailSender = criarMailSender(email);
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

        helper.setTo(destinatarios);
        helper.setFrom(email.getRemetEmail(), email.getRemetNome());
        helper.setSubject(email.getAssunto());

        helper.setText(email.getMensagem(), email.getMensagem().startsWith("<html"));

        adicionarAnexos(helper, anexos);

        mailSender.send(message);
    }

    private void enviarEmailIndividual(EmailsEnviar email, String[] destinatarios,
                                     List<EmailsEnviarAnexo> anexos) throws Exception {
        
        JavaMailSender mailSender = criarMailSender(email);

        for (String destinatario : destinatarios) {
            if (destinatario.trim().isEmpty()) {
                continue;
            }

            log.info("Enviando {} para {}", email.getAssunto(), destinatario);
            logToFile("Enviando " + email.getAssunto() + " para " + destinatario, emailConfig.getEmailLogFile());

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setTo(destinatario);
            helper.setFrom(email.getRemetEmail(), email.getRemetNome());
            helper.setSubject(email.getAssunto());

            if (email.getMensagem().startsWith("<html")) {
                helper.setText(email.getMensagem(), true);
            } else {
                helper.setText(email.getMensagem(), false);
            }

            adicionarAnexos(helper, anexos);
            mailSender.send(message);
        }
    }

    private JavaMailSender criarMailSender(EmailsEnviar email) {
        if (email.getSmtp() != null && !email.getSmtp().equals(emailConfig.getDefaultSmtp())) {
            return emailConfig.createCustomMailSender(
                email.getSmtp(),
                email.getAutLogin(),
                email.getAutSenha(),
                email.getPorta() != null ? email.getPorta() : emailConfig.getDefaultPort()
            );
        }
        
        return defaultMailSender;
    }


    private void adicionarAnexos(MimeMessageHelper helper, List<EmailsEnviarAnexo> anexos) throws Exception {
        for (EmailsEnviarAnexo anexo : anexos) {
            String endAnexo = Objects.nonNull(anexo.getEndAnexo()) ? basePath + anexo.getEndAnexo().trim() : null;
            File existeAnexo = new File(endAnexo);
            if (existeAnexo.exists() && existeAnexo.isFile()) {
                FileSystemResource file = new FileSystemResource(endAnexo);
                helper.addAttachment(anexo.getNomeAnexo(), file);
                log.debug("Anexo adicionado: {}", anexo.getNomeAnexo());
            } else {
                log.warn("Anexo não encontrado: {}", endAnexo);
            }
        }
    }


    private void marcarEmailComoFalha(EmailsEnviar email, String erro) {
        try {
            email.marcarComoFalha();
            emailRepository.save(email);
            
            String mensagemErro = "FALHA AO ENVIAR " + email.getAssunto() + " PARA " + email.getDestEmail() + 
                                 "\r\n" + erro;
            logToFile(mensagemErro, emailConfig.getErrorLogFile());
            
        } catch (Exception e) {
            log.error("Erro ao marcar email como falha: {}", e.getMessage(), e);
        }
    }

    private void logToFile(String mensagem, String arquivo) {
        if (!emailConfig.isLogEmailsEnabled()) {
            return;
        }

        try {
            Path logPath = Paths.get(arquivo);
            Files.createDirectories(logPath.getParent());
            
            String timestamp = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE) + " " +
                             LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            String logLine = timestamp + " - " + mensagem + System.lineSeparator();
            
            Files.write(logPath, logLine.getBytes(), 
                       java.nio.file.StandardOpenOption.CREATE, 
                       java.nio.file.StandardOpenOption.APPEND);
                       
        } catch (IOException e) {
            log.error("Erro ao escrever log em arquivo {}: {}", arquivo, e.getMessage());
        }
    }

    public EmailStats getEmailStats() {
        long pendentes = emailRepository.countEmailsPendentes();
        long enviadosHoje = emailRepository.findEmailsEnviadosNaData(LocalDate.now()).size();
        long comFalha = emailRepository.findEmailsComFalha().size();
        
        return new EmailStats(pendentes, enviadosHoje, comFalha);
    }


    public record EmailStats(long pendentes, long enviadosHoje, long comFalha) {}
}
