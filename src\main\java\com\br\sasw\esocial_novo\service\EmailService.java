package com.br.sasw.esocial_novo.service;

import com.br.sasw.esocial_novo.config.EmailConfig;
import com.br.sasw.esocial_novo.entity.EmailsEnviar;
import com.br.sasw.esocial_novo.entity.EmailsEnviarAnexo;
import com.br.sasw.esocial_novo.repository.EmailsEnviarRepository;
import com.br.sasw.esocial_novo.repository.EmailsEnviarAnexoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.mail.internet.MimeMessage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Serviço principal para envio de emails
 * Substitui a lógica do btnEmailActionPerformed com uma abordagem moderna e robusta
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EmailService {

    private final EmailsEnviarRepository emailRepository;
    private final EmailsEnviarAnexoRepository anexoRepository;
    private final EmailConfig emailConfig;
    private final JavaMailSender defaultMailSender;

    /**
     * Processa todos os emails pendentes
     * Equivale ao loop principal do método btnEmailActionPerformed
     */
    @Transactional
    public void processarEmailsPendentes() {
        try {
            List<EmailsEnviar> emailsPendentes = emailRepository.findEmailsPendentes();
            
            if (emailsPendentes.isEmpty()) {
                log.info("Nenhum email pendente para envio");
                return;
            }

            log.info("Processando {} emails pendentes", emailsPendentes.size());
            logToFile("Emails a enviar: " + emailsPendentes.size(), emailConfig.getEmailLogFile());

            for (EmailsEnviar email : emailsPendentes) {
                try {
                    processarEmail(email);
                } catch (Exception e) {
                    log.error("Erro ao processar email sequência {}: {}", email.getSequencia(), e.getMessage(), e);
                    marcarEmailComoFalha(email, e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("Erro geral no processamento de emails: {}", e.getMessage(), e);
        }
    }

    /**
     * Processa um email individual
     */
    private void processarEmail(EmailsEnviar email) throws Exception {
        // Verifica se já foi enviado
        if (email.isEnviado()) {
            return;
        }

        // Busca anexos
        List<EmailsEnviarAnexo> anexos = anexoRepository.findBySequenciaOrderByOrdem(email.getSequencia());
        
        // Limpa e valida destinatários
        String[] destinatarios = email.getDestinatariosLimpos();
        if (destinatarios.length == 0) {
            log.warn("Email sequência {} não possui destinatários válidos", email.getSequencia());
            return;
        }

        // Log específico para GSI
        if (isEmailGSI(email)) {
            logToFile("AUT >> Enviando " + email.getAssunto() + " para " + email.getDestEmail(), 
                     emailConfig.getGsiLogFile());
        }

        // Determina o tipo de envio baseado no parâmetro
        if (email.getParametro() != null && !email.getParametro().trim().isEmpty()) {
            // Envio para múltiplos destinatários (como no projeto antigo)
            enviarEmailMultiplosDestinatarios(email, destinatarios, anexos);
        } else {
            // Envio individual para cada destinatário
            enviarEmailIndividual(email, destinatarios, anexos);
        }

        // Marca como enviado
        email.marcarComoEnviado();
        emailRepository.save(email);

        log.info("Email '{}' enviado com sucesso para {}", email.getAssunto(), email.getDestEmail());
        logToFile(email.getAssunto() + " enviado com sucesso para " + email.getDestEmail(), 
                 emailConfig.getEmailLogFile());
    }

    /**
     * Envia email para múltiplos destinatários de uma vez
     */
    private void enviarEmailMultiplosDestinatarios(EmailsEnviar email, String[] destinatarios, 
                                                  List<EmailsEnviarAnexo> anexos) throws Exception {
        
        log.info("Enviando {} para múltiplos destinatários: {}", email.getAssunto(), String.join(", ", destinatarios));
        logToFile("Enviando " + email.getAssunto() + " para " + destinatarios[0], emailConfig.getEmailLogFile());

        JavaMailSender mailSender = criarMailSender(email);
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

        // Configurar destinatários
        helper.setTo(destinatarios);
        helper.setFrom(email.getRemetEmail(), email.getRemetNome());
        helper.setSubject(email.getAssunto());

        // Configurar conteúdo
        if (email.getMensagem().startsWith("<html")) {
            helper.setText(email.getMensagem(), true);
        } else {
            helper.setText(email.getMensagem(), false);
        }

        // Adicionar anexos
        adicionarAnexos(helper, anexos);

        // Enviar
        mailSender.send(message);
    }

    /**
     * Envia email individualmente para cada destinatário
     */
    private void enviarEmailIndividual(EmailsEnviar email, String[] destinatarios, 
                                     List<EmailsEnviarAnexo> anexos) throws Exception {
        
        JavaMailSender mailSender = criarMailSender(email);

        for (String destinatario : destinatarios) {
            if (destinatario.trim().isEmpty()) {
                continue;
            }

            log.info("Enviando {} para {}", email.getAssunto(), destinatario);
            logToFile("Enviando " + email.getAssunto() + " para " + destinatario, emailConfig.getEmailLogFile());

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setTo(destinatario);
            helper.setFrom(email.getRemetEmail(), email.getRemetNome());
            helper.setSubject(email.getAssunto());

            if (email.getMensagem().startsWith("<html")) {
                helper.setText(email.getMensagem(), true);
            } else {
                helper.setText(email.getMensagem(), false);
            }

            adicionarAnexos(helper, anexos);
            mailSender.send(message);
        }
    }

    /**
     * Cria um JavaMailSender customizado baseado nas configurações do email
     */
    private JavaMailSender criarMailSender(EmailsEnviar email) {
        // Se o email tem configurações específicas, usa elas
        if (email.getSmtp() != null && !email.getSmtp().equals(emailConfig.getDefaultSmtp())) {
            return emailConfig.createCustomMailSender(
                email.getSmtp(),
                email.getAutLogin(),
                email.getAutSenha(),
                email.getPorta() != null ? email.getPorta() : emailConfig.getDefaultPort()
            );
        }
        
        return defaultMailSender;
    }

    /**
     * Adiciona anexos ao email
     */
    private void adicionarAnexos(MimeMessageHelper helper, List<EmailsEnviarAnexo> anexos) throws Exception {
        for (EmailsEnviarAnexo anexo : anexos) {
            if (anexo.anexoExiste()) {
                FileSystemResource file = new FileSystemResource(anexo.getCaminhoCompletoAnexo());
                helper.addAttachment(anexo.getNomeAnexo(), file);
                log.debug("Anexo adicionado: {}", anexo.getNomeAnexo());
            } else {
                log.warn("Anexo não encontrado: {}", anexo.getCaminhoCompletoAnexo());
            }
        }
    }

    /**
     * Marca email como falha
     */
    private void marcarEmailComoFalha(EmailsEnviar email, String erro) {
        try {
            email.marcarComoFalha();
            emailRepository.save(email);
            
            String mensagemErro = "FALHA AO ENVIAR " + email.getAssunto() + " PARA " + email.getDestEmail() + 
                                 "\r\n" + erro;
            logToFile(mensagemErro, emailConfig.getErrorLogFile());
            
        } catch (Exception e) {
            log.error("Erro ao marcar email como falha: {}", e.getMessage(), e);
        }
    }

    /**
     * Verifica se é email do grupo GSI
     */
    private boolean isEmailGSI(EmailsEnviar email) {
        return "SATPROSECUR".equals(email.getParametro()) || "SATGLOVAL".equals(email.getParametro());
    }

    /**
     * Escreve log em arquivo (mantém compatibilidade com sistema antigo)
     */
    private void logToFile(String mensagem, String arquivo) {
        if (!emailConfig.isLogEmailsEnabled()) {
            return;
        }

        try {
            Path logPath = Paths.get(arquivo);
            Files.createDirectories(logPath.getParent());
            
            String timestamp = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE) + " " +
                             LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            String logLine = timestamp + " - " + mensagem + System.lineSeparator();
            
            Files.write(logPath, logLine.getBytes(), 
                       java.nio.file.StandardOpenOption.CREATE, 
                       java.nio.file.StandardOpenOption.APPEND);
                       
        } catch (IOException e) {
            log.error("Erro ao escrever log em arquivo {}: {}", arquivo, e.getMessage());
        }
    }

    /**
     * Retorna estatísticas de emails
     */
    public EmailStats getEmailStats() {
        long pendentes = emailRepository.countEmailsPendentes();
        long enviadosHoje = emailRepository.findEmailsEnviadosNaData(LocalDate.now()).size();
        long comFalha = emailRepository.findEmailsComFalha().size();
        
        return new EmailStats(pendentes, enviadosHoje, comFalha);
    }

    /**
     * Classe para estatísticas de email
     */
    public record EmailStats(long pendentes, long enviadosHoje, long comFalha) {}
}
