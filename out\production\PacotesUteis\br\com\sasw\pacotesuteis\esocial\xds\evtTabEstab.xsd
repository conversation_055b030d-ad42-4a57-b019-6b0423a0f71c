﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtTabEstab/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtTabEstab/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtTabEstab">
                    <xs:annotation>
                        <xs:documentation>Evento Tabela de Estabelecimentos, Obras ou Orgaos Publicos</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeCadastro">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="infoEstab">
                                <xs:annotation>
                                    <xs:documentation>Informacoes do Estabelecimento ou obra</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:choice>
                                            <xs:element name="inclusao">
                                                <xs:annotation>
                                                    <xs:documentation>Inclusao de novas informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideEstab" type="TIdeEstab">
                                                            <xs:annotation>
                                                                <xs:documentation>Identificacao do estabelecimento, obra ou Orgao publico e validade das informacoes</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="dadosEstab" type="TDadosEstab">
                                                            <xs:annotation>
                                                                <xs:documentation>Detalhamento das informacoes do estabelecimento, obra ou Orgao publico</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="alteracao">
                                                <xs:annotation>
                                                    <xs:documentation>Alteracao das informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideEstab" type="TIdeEstab">
                                                            <xs:annotation>
                                                                <xs:documentation>Identificacao do estabelecimento, obra ou Orgao publico e validade das informacoes</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="dadosEstab" type="TDadosEstab">
                                                            <xs:annotation>
                                                                <xs:documentation>Detalhamento das informacoes do estabelecimento, obra ou Orgao publico</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="novaValidade" type="TPeriodoValidade" minOccurs="0">
                                                            <xs:annotation>
                                                                <xs:documentation>Novo periodo de validade das informacoes</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                            <xs:element name="exclusao">
                                                <xs:annotation>
                                                    <xs:documentation>Exclusao das informacoes</xs:documentation>
                                                </xs:annotation>
                                                <xs:complexType>
                                                    <xs:sequence>
                                                        <xs:element name="ideEstab" type="TIdeEstab">
                                                            <xs:annotation>
                                                                <xs:documentation>Identificacao do estabelecimento, obra ou Orgao publico</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:sequence>
                                                </xs:complexType>
                                            </xs:element>
                                        </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeCadastro">
        <xs:annotation>
            <xs:documentation>Identificacao de evento de cadastro/tabelas</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Numero de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TIdeEstab">
        <xs:annotation>
            <xs:documentation>Identificacao do estabelecimento, obra ou Orgao publico e validade das informacoes</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Numero de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="iniValid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Inicio da validade (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fimValid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo final da validade, no formato (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TDadosEstab">
        <xs:annotation>
            <xs:documentation>Detalhamento das informacoes do estabelecimento/obra</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="cnaePrep">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo do CNAE</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:integer">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="aliqGilrat">
                <xs:annotation>
                    <xs:documentation>Informacoes de Apuracao da aliquota Gilrat do Estabelecimento</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="aliqRat">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Aliquota RAT</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:integer">
                                    <xs:pattern value="\d"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="fap" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>FAP</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="5"/>
                                    <xs:fractionDigits value="4"/>
                                    <xs:minInclusive value="0.5"/>
                                    <xs:maxInclusive value="2"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="aliqRatAjust" minOccurs="0">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Aliquota do RAT apOs ajuste pelo FAP</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="5"/>
                                    <xs:fractionDigits value="4"/>
                                    <xs:minInclusive value="0.5"/>
                                    <xs:maxInclusive value="6"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="procAdmJudRat" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Processo administrativo/judicial relativo à aliquota RAT</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="tpProc">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Tipo de Processo</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:byte">
                                                <xs:pattern value="\d"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="nrProc">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Numero do Processo</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:minLength value="2"/>
                                                <xs:maxLength value="21"/>
                                                <xs:whiteSpace value="preserve"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="codSusp">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>COdigo do Indicativo da Suspensao</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:integer">
                                                <xs:pattern value="\d{1,14}"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="procAdmJudFap" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Processo administrativo/judicial relativo ao FAP</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="tpProc">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Tipo de Processo</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:byte">
                                                <xs:pattern value="\d"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="nrProc">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Numero do Processo</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:minLength value="2"/>
                                                <xs:maxLength value="21"/>
                                                <xs:whiteSpace value="preserve"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="codSusp">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>COdigo do Indicativo da Suspensao</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:integer">
                                                <xs:pattern value="\d{1,14}"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="infoCaepf" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Informacoes relativas ao CAEPF</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="tpCaepf">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Tipo de CAEPF</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:byte">
                                    <xs:pattern value="\d"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="infoObra" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>Indicativo de substituicao da contribuicao Patronal - Obra de Construcao Civil</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="indSubstPatrObra">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Substituicao da CP patronal de Obra</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:byte">
                                    <xs:pattern value="\d"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="infoTrab">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="regPt">
                            <xs:simpleType>
                                <xs:annotation>
                                    <xs:documentation>Registro de ponto</xs:documentation>
                                </xs:annotation>
                                <xs:restriction base="xs:byte">
                                    <xs:pattern value="\d"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="infoApr">
                            <xs:annotation>
                                <xs:documentation>Informacoes relacionadas à contratacao de aprendiz</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="contApr">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Indicativo de contratacao de aprendiz</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:byte">
                                                <xs:pattern value="\d"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="nrProcJud" minOccurs="0">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Identificacao do Numero do Processo Judicial</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:length value="20"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="contEntEd" minOccurs="0">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Informar se o estabelecimento realiza a contratacao de aprendiz por intermédio de entidade educativa sem fins lucrativos, que tenha por objetivo a assistencia ao adolescente e à educacao profissional (art 430, inciso II, CLT)</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:pattern value="[N|S]"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="infoEntEduc" minOccurs="0" maxOccurs="99">
                                        <xs:annotation>
                                            <xs:documentation>Identificacao da(s) entidade(s) educativa(s) ou de pratica desportiva</xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="nrInsc">
                                                    <xs:simpleType>
                                                        <xs:annotation>
                                                            <xs:documentation>Numero de Inscricao</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:restriction base="xs:string">
                                                            <xs:pattern value="\d{8,14}"/>
                                                        </xs:restriction>
                                                    </xs:simpleType>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="infoPCD" minOccurs="0">
                            <xs:annotation>
                                <xs:documentation>Informacoes sobre a contratacao de pessoa com deficiencia (PCD)</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="contPCD">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Indicativo de contratacao de PCD</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:byte">
                                                <xs:pattern value="\d"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="nrProcJud" minOccurs="0">
                                        <xs:simpleType>
                                            <xs:annotation>
                                                <xs:documentation>Identificacao do Numero do Processo Judicial</xs:documentation>
                                            </xs:annotation>
                                            <xs:restriction base="xs:string">
                                                <xs:length value="20"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TPeriodoValidade">
        <xs:annotation>
            <xs:documentation>Periodo de validade das informacoes</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="iniValid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Inicio da validade (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="fimValid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo final da validade, no formato (AAAA-MM)</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
