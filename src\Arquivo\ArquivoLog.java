package Arquivo;

/**
 *
 * <AUTHOR>
 */
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;

public class ArquivoLog {

    private String nomeClasse;

    public ArquivoLog() {
    }

    public ArquivoLog(String nomeClasse) {
        this.nomeClasse = nomeClasse;
    }

    /**
     * Grava uma linha de log para o sistema - usar apenas para log
     *
     * @param Mensagem - Mensagem que sera gravada
     * @param NomeArquivo - nome do arquivo de log
     */
    public void Grava(String Mensagem, String NomeArquivo) {
        try {
            // Gravando no arquivo  
            File arquivo = new File(NomeArquivo);
            File diretorio = new File(NomeArquivo.substring(0, NomeArquivo.lastIndexOf("\\")));
            BufferedWriter writer;
            if (!diretorio.exists()) {
                diretorio.mkdirs(); // cria diretórios caso não estejam criados
            }
            if (!this.VerificaExistencia(arquivo)) {
                writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(NomeArquivo), StandardCharsets.UTF_8));
            } else {
                writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(NomeArquivo, true), StandardCharsets.UTF_8));
            }
            String texto = "********** Mensagem - " + this.getDataHora() + " *********************************************************";
            writer.newLine();
            writer.write(texto);
            writer.newLine();
            writer.write(Mensagem);
            writer.newLine();
            writer.close();
        } catch (Exception e) {

        }
    }

    /**
     * Grava no log os métodos executados
     *
     * @param Mensagem - Mensagem que sera gravada
     * @param NomeArquivo - nome do arquivo de log
     */
    public void GravaMetodos(String Mensagem, String NomeArquivo) {
        try {
            // Gravando no arquivo  
            File arquivo = new File(NomeArquivo);
            File diretorio = new File(NomeArquivo.substring(0, NomeArquivo.lastIndexOf("\\")));
            BufferedWriter writer;
            if (!diretorio.exists()) {
                diretorio.mkdirs(); // cria diretórios caso não estejam criados
            }
            if (!this.VerificaExistencia(arquivo)) {
                writer = new BufferedWriter(new FileWriter(NomeArquivo));
            } else {
                writer = new BufferedWriter(new FileWriter(NomeArquivo, true));
            }
            String texto = "********** Mensagem - " + this.getDataHora() + " *********************************************************";
            writer.newLine();
            writer.write(texto);
            writer.newLine();
            writer.write(nomeClasse);
            writer.newLine();
            if (Thread.currentThread().getStackTrace()[2].getMethodName().equals("gerarLog")) {
                if (Thread.currentThread().getStackTrace()[4].getMethodName().equals("invoke0")) {
                    writer.write(Thread.currentThread().getStackTrace()[3].getMethodName());
                } else {
                    writer.write(Thread.currentThread().getStackTrace()[4].getMethodName() + " - " + Thread.currentThread().getStackTrace()[3].getMethodName());
                }
                writer.newLine();
            } else {
                if (Thread.currentThread().getStackTrace()[3].getMethodName().equals("invoke0")) {
                    writer.write(Thread.currentThread().getStackTrace()[2].getMethodName());
                } else {
                    writer.write(Thread.currentThread().getStackTrace()[3].getMethodName() + " - " + Thread.currentThread().getStackTrace()[2].getMethodName());
                }
                writer.newLine();
            }
            writer.write(Mensagem);
            writer.newLine();
            writer.close();
        } catch (Exception e) {

        }
    }

    private boolean VerificaExistencia(File arquivo) {
        if (arquivo.exists()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Grava uma linha no arquivo
     *
     * @param Mensagem - Conteudo da linha a ser gravada
     * @param NomeArquivo - Nome do arquivo a ser criado ou adicionada a linha
     * no final do arquivo
     */
    public void GravaDados(String Mensagem, String NomeArquivo) throws Exception {
        try {
            // Gravando no arquivo  
            File arquivo;
            arquivo = new File(NomeArquivo);
            BufferedWriter writer;
            File diretorio = new File(NomeArquivo.substring(0, NomeArquivo.lastIndexOf("\\")));
            if (!diretorio.exists()) {
                diretorio.mkdirs(); // cria diretórios caso não estejam criados
            }
            if (!this.VerificaExistencia(arquivo)) {
                writer = new BufferedWriter(new FileWriter(NomeArquivo));
            } else {
                writer = new BufferedWriter(new FileWriter(NomeArquivo, true));
            }
            writer.newLine();
            writer.write(Mensagem);
            writer.close();

        } catch (Exception e) {
            throw new Exception("Erro ao gravar dados no arquivo - " + e.getMessage());
        }
    }

    /**
     * Grava uma linha no arquivo
     *
     * @param Mensagem - Conteudo da linha a ser gravada
     * @param NomeArquivo - Nome do arquivo a ser criado ou adicionada a linha
     * no final do arquivo
     */
    public void GravaDadosNovo(String Mensagem, String NomeArquivo) throws Exception {
        try {
            // Gravando no arquivo  
            File arquivo;
            arquivo = new File(NomeArquivo);
            BufferedWriter writer;
            writer = new BufferedWriter(new FileWriter(NomeArquivo));
            writer.write(Mensagem);
            writer.close();

        } catch (Exception e) {
            throw new Exception("Erro ao gravar dados no arquivo - " + e.getMessage());
        }
    }

    /**
     * Grava um uma linha de Separador com
     *
     ********
     * @param NomeArquivo - Nome do arquivo onde sera incluso o separador
     */
    public void GravaSeparador(String NomeArquivo) throws Exception {
        try {
            // Gravando no arquivo  
            File arquivo;
            arquivo = new File(NomeArquivo);
            BufferedWriter writer;
            File diretorio = new File(NomeArquivo.substring(0, NomeArquivo.lastIndexOf("\\")));
            if (!diretorio.exists()) {
                diretorio.mkdirs(); // cria diretórios caso não estejam criados
            }
            if (!this.VerificaExistencia(arquivo)) {
                writer = new BufferedWriter(new FileWriter(NomeArquivo));
            } else {
                writer = new BufferedWriter(new FileWriter(NomeArquivo, true));
            }
            writer.newLine();
            writer.write("**************************************************");
            writer.close();

        } catch (Exception e) {
            throw new Exception("Erro ao gravar separador no arquivo - " + e.getMessage());
        }
    }

    private String getDataHora() {
        Date data_atual;
        SimpleDateFormat data_formatada;
        String data;

        data_atual = new Date();
        data_formatada = new SimpleDateFormat("dd/MM/yyyy");
        data = data_formatada.format(data_atual);
        data_formatada = new SimpleDateFormat("HH:mm:ssS");
        data += "-" + data_formatada.format(data_atual);
        return data;
    }

    /**
     * Grava uma linha de log para o sistema - usar apenas para log
     *
     * @param Mensagem - Mensagem que sera gravada
     * @param NomeArquivo - nome do arquivo de log
     */
    public void GravaHTML(String Mensagem, String NomeArquivo) {
        try {
            // Gravando no arquivo  
            File arquivo = new File(NomeArquivo);
            File diretorio = new File(NomeArquivo.substring(0, NomeArquivo.lastIndexOf("\\")));
            BufferedWriter writer;
            if (!diretorio.exists()) {
                diretorio.mkdirs(); // cria diretórios caso não estejam criados
            }
            if (!this.VerificaExistencia(arquivo)) {
                writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(NomeArquivo), StandardCharsets.UTF_8));
            } else {
                writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(NomeArquivo, true), StandardCharsets.UTF_8));
            }
            String texto = "<!--\n"
                    + "//SASW Tecnologia\n"
                    + "//SATMOB\n"
                    + "//FrmFaceAutentica -HTML\n"
                    + "//Autor : SASW\n"                    
                    +"//-->\n";
            writer.newLine();
            writer.write(texto);
            writer.newLine();
            writer.write(Mensagem);
            writer.newLine();
            writer.close();
        } catch (Exception e) {

        }
    }
}
