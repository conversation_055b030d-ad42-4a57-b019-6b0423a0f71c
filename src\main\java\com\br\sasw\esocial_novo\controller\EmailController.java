package com.br.sasw.esocial_novo.controller;

import com.br.sasw.esocial_novo.entity.EmailsEnviar;
import com.br.sasw.esocial_novo.repository.EmailsEnviarRepository;
import com.br.sasw.esocial_novo.service.EmailSchedulerService;
import com.br.sasw.esocial_novo.service.EmailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/emails")
@RequiredArgsConstructor
@Slf4j
public class EmailController {

    private final EmailSchedulerService schedulerService;
    private final EmailService emailService;
    private final EmailsEnviarRepository emailRepository;

    @GetMapping("/status")
    public ResponseEntity<EmailSchedulerService.SchedulerStatus> getStatus() {
        return ResponseEntity.ok(schedulerService.getStatus());
    }


    @PostMapping("/{sequencia}/reprocessar")
    public ResponseEntity<Map<String, String>> reprocessarEmail(@PathVariable String sequencia) {
        try {
            var email = emailRepository.findById(new java.math.BigDecimal(sequencia));
            if (email.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            EmailsEnviar emailEntity = email.get();
            emailEntity.setFlagEnviado("");
            emailEntity.setDtEnvio(null);
            emailEntity.setHrEnvio("");
            emailRepository.save(emailEntity);

            log.info("Email sequência {} marcado para reprocessamento", sequencia);
            
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Email marcado para reprocessamento"
            ));
            
        } catch (Exception e) {
            log.error("Erro ao reprocessar email {}: {}", sequencia, e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Erro ao reprocessar email: " + e.getMessage()
            ));
        }
    }

}
