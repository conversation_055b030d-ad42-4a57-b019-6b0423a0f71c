package com.br.sasw.esocial_novo.controller;

import com.br.sasw.esocial_novo.entity.EmailsEnviar;
import com.br.sasw.esocial_novo.repository.EmailsEnviarRepository;
import com.br.sasw.esocial_novo.service.EmailSchedulerService;
import com.br.sasw.esocial_novo.service.EmailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Controller REST para gerenciamento do sistema de emails
 * Fornece endpoints para monitoramento e controle, substituindo a interface Swing do projeto antigo
 */
@RestController
@RequestMapping("/api/emails")
@RequiredArgsConstructor
@Slf4j
public class EmailController {

    private final EmailSchedulerService schedulerService;
    private final EmailService emailService;
    private final EmailsEnviarRepository emailRepository;

    /**
     * Retorna o status atual do sistema de emails
     * Equivale às informações mostradas na tela principal do projeto antigo
     */
    @GetMapping("/status")
    public ResponseEntity<EmailSchedulerService.SchedulerStatus> getStatus() {
        return ResponseEntity.ok(schedulerService.getStatus());
    }

    /**
     * Inicia o processamento de emails (equivale ao botão "Iniciar" do projeto antigo)
     */
    @PostMapping("/iniciar")
    public ResponseEntity<Map<String, String>> iniciarProcessamento() {
        try {
            schedulerService.iniciarScheduler();
            log.info("Processamento de emails iniciado via API");
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Processamento de emails iniciado"
            ));
        } catch (Exception e) {
            log.error("Erro ao iniciar processamento de emails: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Erro ao iniciar processamento: " + e.getMessage()
            ));
        }
    }

    /**
     * Para o processamento de emails (equivale ao botão "Parar" do projeto antigo)
     */
    @PostMapping("/parar")
    public ResponseEntity<Map<String, String>> pararProcessamento() {
        try {
            schedulerService.pararScheduler();
            log.info("Processamento de emails parado via API");
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Processamento de emails parado"
            ));
        } catch (Exception e) {
            log.error("Erro ao parar processamento de emails: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Erro ao parar processamento: " + e.getMessage()
            ));
        }
    }

    /**
     * Força o processamento imediato de emails
     */
    @PostMapping("/processar-agora")
    public ResponseEntity<Map<String, String>> processarAgora() {
        try {
            schedulerService.processarEmailsImediato();
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Emails processados com sucesso"
            ));
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", e.getMessage()
            ));
        } catch (Exception e) {
            log.error("Erro ao processar emails imediatamente: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Erro ao processar emails: " + e.getMessage()
            ));
        }
    }

    /**
     * Lista emails pendentes
     */
    @GetMapping("/pendentes")
    public ResponseEntity<List<EmailsEnviar>> getEmailsPendentes(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("sequencia").ascending());
        List<EmailsEnviar> emails = emailRepository.findEmailsPendentes();
        
        // Simula paginação (idealmente seria feita no repositório)
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), emails.size());
        List<EmailsEnviar> pageContent = emails.subList(start, end);
        
        return ResponseEntity.ok(pageContent);
    }

    /**
     * Lista emails enviados hoje
     */
    @GetMapping("/enviados-hoje")
    public ResponseEntity<List<EmailsEnviar>> getEmailsEnviadosHoje() {
        List<EmailsEnviar> emails = emailRepository.findEmailsEnviadosNaData(LocalDate.now());
        return ResponseEntity.ok(emails);
    }

    /**
     * Lista emails com falha
     */
    @GetMapping("/com-falha")
    public ResponseEntity<List<EmailsEnviar>> getEmailsComFalha(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        List<EmailsEnviar> emails = emailRepository.findEmailsComFalha();
        
        // Simula paginação
        int start = page * size;
        int end = Math.min((start + size), emails.size());
        List<EmailsEnviar> pageContent = emails.subList(start, end);
        
        return ResponseEntity.ok(pageContent);
    }

    /**
     * Busca emails por cliente
     */
    @GetMapping("/cliente/{codCli}")
    public ResponseEntity<List<EmailsEnviar>> getEmailsCliente(
            @PathVariable String codCli,
            @RequestParam String codFil,
            @RequestParam String parametro,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dataInicio,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dataFim) {
        
        List<EmailsEnviar> emails = emailRepository.findEmailsCliente(codCli, codFil, parametro, dataInicio, dataFim);
        return ResponseEntity.ok(emails);
    }

    /**
     * Retorna estatísticas detalhadas
     */
    @GetMapping("/estatisticas")
    public ResponseEntity<Map<String, Object>> getEstatisticas() {
        EmailService.EmailStats stats = emailService.getEmailStats();
        
        Map<String, Object> estatisticas = Map.of(
            "pendentes", stats.pendentes(),
            "enviadosHoje", stats.enviadosHoje(),
            "comFalha", stats.comFalha(),
            "schedulerAtivo", schedulerService.isSchedulerAtivo(),
            "processandoAgora", schedulerService.isProcessandoEmails(),
            "dataConsulta", LocalDate.now()
        );
        
        return ResponseEntity.ok(estatisticas);
    }

    /**
     * Reprocessa um email específico (marca como pendente novamente)
     */
    @PostMapping("/{sequencia}/reprocessar")
    public ResponseEntity<Map<String, String>> reprocessarEmail(@PathVariable String sequencia) {
        try {
            var email = emailRepository.findById(new java.math.BigDecimal(sequencia));
            if (email.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            EmailsEnviar emailEntity = email.get();
            emailEntity.setFlagEnviado(""); // Marca como pendente
            emailEntity.setDtEnvio(null);
            emailEntity.setHrEnvio(null);
            emailRepository.save(emailEntity);

            log.info("Email sequência {} marcado para reprocessamento", sequencia);
            
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "Email marcado para reprocessamento"
            ));
            
        } catch (Exception e) {
            log.error("Erro ao reprocessar email {}: {}", sequencia, e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Erro ao reprocessar email: " + e.getMessage()
            ));
        }
    }

    /**
     * Endpoint de saúde para monitoramento
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        boolean schedulerAtivo = schedulerService.isSchedulerAtivo();
        boolean processando = schedulerService.isProcessandoEmails();
        long emailsPendentes = emailRepository.countEmailsPendentes();
        
        Map<String, Object> health = Map.of(
            "status", schedulerAtivo ? "UP" : "DOWN",
            "scheduler", Map.of(
                "ativo", schedulerAtivo,
                "processando", processando
            ),
            "emails", Map.of(
                "pendentes", emailsPendentes
            ),
            "timestamp", java.time.Instant.now()
        );
        
        return ResponseEntity.ok(health);
    }
}
