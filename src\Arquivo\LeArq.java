package Arquivo;

import java.io.BufferedReader;
import java.io.FileReader;

/**
 *
 * <AUTHOR>
 */
public class LeArq {

    private String Linha;
    private FileReader reader;
    private BufferedReader leitor;

    /**
     * Abre um arquivo
     *
     * @param caminho - string com o caminho e nome do arquivo
     */
    public void AbreArquivo(String caminho) {
        try {
            reader = new FileReader(caminho);
            leitor = new BufferedReader(reader);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Retorna a próxiam linha do arquivo
     *
     * @return - string com a proxima linha do arquivo
     */
    public String getLinha() {
        try {
            Linha = leitor.readLine();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Linha;
    }

    /**
     * Retorna um buffer com o arquivo selecionado
     *
     * @return - string com o arquivo inteiro na forma de buffer
     */
    public String getBuffer() {
        String buff = "";
        String linha = this.getLinha();
        try {
            while (linha != null) {
                buff += linha + "\r\n";
                linha = this.getLinha();
            }
            this.FechaArquivo();
        } catch (Exception e) {
            buff = "";
            this.FechaArquivo();
            e.printStackTrace();
        }
        return buff;
    }

    /**
     * Fecha o arquivo.
     */
    public void FechaArquivo() {
        try {
            leitor.close();
            reader.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
