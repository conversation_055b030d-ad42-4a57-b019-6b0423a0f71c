
package br.gov.go.goiania.nfse.ws;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the br.gov.go.goiania.nfse.ws package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: br.gov.go.goiania.nfse.ws
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link GerarNfse }
     * 
     */
    public GerarNfse createGerarNfse() {
        return new GerarNfse();
    }

    /**
     * Create an instance of {@link GerarNfseResponse }
     * 
     */
    public GerarNfseResponse createGerarNfseResponse() {
        return new GerarNfseResponse();
    }

    /**
     * Create an instance of {@link ConsultarNfseRps }
     * 
     */
    public ConsultarNfseRps createConsultarNfseRps() {
        return new ConsultarNfseRps();
    }

    /**
     * Create an instance of {@link ConsultarNfseRpsResponse }
     * 
     */
    public ConsultarNfseRpsResponse createConsultarNfseRpsResponse() {
        return new ConsultarNfseRpsResponse();
    }

}
