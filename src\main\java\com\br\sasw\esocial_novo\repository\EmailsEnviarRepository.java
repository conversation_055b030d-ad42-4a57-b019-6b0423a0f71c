package com.br.sasw.esocial_novo.repository;

import com.br.sasw.esocial_novo.entity.EmailsEnviar;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Repositório para a entidade EmailsEnviar
 * Substitui o EmailsEnviarDao do projeto antigo com uma abordagem moderna usando Spring Data JPA
 */
@Repository
public interface EmailsEnviarRepository extends JpaRepository<EmailsEnviar, BigDecimal> {

    /**
     * Busca emails pendentes de envio (equivalente ao método Consulta do DAO antigo)
     * @return Lista de emails com flag_enviado vazio ou nulo
     */
    @Query("SELECT e FROM EmailsEnviar e WHERE e.flagEnviado IS NULL OR e.flagEnviado = '' ORDER BY e.sequencia")
    List<EmailsEnviar> findEmailsPendentes();

    /**
     * Busca emails por parâmetro específico (GSI, SATGLOVAL, SATPROSECUR)
     * @param parametros Lista de parâmetros para filtrar
     * @return Lista de emails filtrados por parâmetro
     */
    @Query("SELECT e FROM EmailsEnviar e WHERE e.flagEnviado <> '*' AND e.parametro IN :parametros ORDER BY e.sequencia")
    List<EmailsEnviar> findEmailsByParametros(@Param("parametros") List<String> parametros);

    /**
     * Busca emails excluindo parâmetros específicos
     * @param parametrosExcluidos Lista de parâmetros a excluir
     * @return Lista de emails excluindo os parâmetros especificados
     */
    @Query("SELECT e FROM EmailsEnviar e WHERE e.flagEnviado <> '*' AND (e.parametro NOT IN :parametrosExcluidos OR e.parametro IS NULL) ORDER BY e.sequencia")
    List<EmailsEnviar> findEmailsExcluindoParametros(@Param("parametrosExcluidos") List<String> parametrosExcluidos);

    /**
     * Busca emails de um cliente específico em um período
     * @param codCli Código do cliente
     * @param codFil Código da filial
     * @param parametro Parâmetro específico
     * @param dataInicio Data de início
     * @param dataFim Data de fim
     * @return Lista de emails do cliente no período
     */
    @Query("SELECT e FROM EmailsEnviar e WHERE e.codCli = :codCli AND e.codFil = :codFil AND e.parametro = :parametro " +
           "AND (:dataInicio IS NULL OR e.dtInclusao >= :dataInicio) " +
           "AND (:dataFim IS NULL OR e.dtInclusao <= :dataFim) " +
           "ORDER BY e.sequencia DESC")
    List<EmailsEnviar> findEmailsCliente(@Param("codCli") String codCli, 
                                        @Param("codFil") String codFil, 
                                        @Param("parametro") String parametro,
                                        @Param("dataInicio") LocalDate dataInicio, 
                                        @Param("dataFim") LocalDate dataFim);

    /**
     * Conta emails pendentes por parâmetro
     * @param parametro Parâmetro para filtrar
     * @return Quantidade de emails pendentes
     */
    @Query("SELECT COUNT(e) FROM EmailsEnviar e WHERE (e.flagEnviado IS NULL OR e.flagEnviado = '') AND e.parametro = :parametro")
    long countEmailsPendentesByParametro(@Param("parametro") String parametro);

    /**
     * Conta total de emails pendentes
     * @return Quantidade total de emails pendentes
     */
    @Query("SELECT COUNT(e) FROM EmailsEnviar e WHERE e.flagEnviado IS NULL OR e.flagEnviado = ''")
    long countEmailsPendentes();

    /**
     * Busca emails enviados hoje
     * @param data Data para filtrar
     * @return Lista de emails enviados na data especificada
     */
    @Query("SELECT e FROM EmailsEnviar e WHERE e.dtEnvio = :data ORDER BY e.hrEnvio DESC")
    List<EmailsEnviar> findEmailsEnviadosNaData(@Param("data") LocalDate data);

    /**
     * Busca emails com falha de envio
     * @return Lista de emails com flag de erro
     */
    @Query("SELECT e FROM EmailsEnviar e WHERE e.flagEnviado = 'e' ORDER BY e.sequencia DESC")
    List<EmailsEnviar> findEmailsComFalha();

    /**
     * Busca próxima sequência disponível
     * @return Próxima sequência para inserção
     */
    @Query("SELECT COALESCE(MAX(e.sequencia), 0) + 1 FROM EmailsEnviar e")
    BigDecimal findProximaSequencia();
}
