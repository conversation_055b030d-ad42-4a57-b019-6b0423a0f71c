package com.br.sasw.esocial_novo.repository;

import com.br.sasw.esocial_novo.entity.EmailsEnviar;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface EmailsEnviarRepository extends JpaRepository<EmailsEnviar, BigDecimal> {

    @Query("SELECT e FROM EmailsEnviar e WHERE e.flagEnviado IS NULL OR e.flagEnviado = '' ORDER BY e.sequencia")
    List<EmailsEnviar> findEmailsPendentes();

    @Query("SELECT e FROM EmailsEnviar e WHERE e.flagEnviado <> '*' AND e.parametro IN :parametros ORDER BY e.sequencia")
    List<EmailsEnviar> findEmailsByParametros(@Param("parametros") List<String> parametros);

    @Query("SELECT e FROM EmailsEnviar e WHERE e.flagEnviado <> '*' AND (e.parametro NOT IN :parametrosExcluidos OR e.parametro IS NULL) ORDER BY e.sequencia")
    List<EmailsEnviar> findEmailsExcluindoParametros(@Param("parametrosExcluidos") List<String> parametrosExcluidos);

    @Query("SELECT e FROM EmailsEnviar e WHERE e.codCli = :codCli AND e.codFil = :codFil AND e.parametro = :parametro " +
           "AND (:dataInicio IS NULL OR e.dtInclusao >= :dataInicio) " +
           "AND (:dataFim IS NULL OR e.dtInclusao <= :dataFim) " +
           "ORDER BY e.sequencia DESC")
    List<EmailsEnviar> findEmailsCliente(@Param("codCli") String codCli, 
                                        @Param("codFil") String codFil, 
                                        @Param("parametro") String parametro,
                                        @Param("dataInicio") LocalDate dataInicio, 
                                        @Param("dataFim") LocalDate dataFim);

    @Query("SELECT COUNT(e) FROM EmailsEnviar e WHERE e.flagEnviado IS NULL OR e.flagEnviado = ''")
    long countEmailsPendentes();

    @Query("SELECT e FROM EmailsEnviar e WHERE e.dtEnvio = :data ORDER BY e.hrEnvio DESC")
    List<EmailsEnviar> findEmailsEnviadosNaData(@Param("data") LocalDate data);

    @Query("SELECT e FROM EmailsEnviar e WHERE e.flagEnviado = 'e' ORDER BY e.sequencia DESC")
    List<EmailsEnviar> findEmailsComFalha();
}
