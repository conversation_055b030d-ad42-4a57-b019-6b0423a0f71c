package com.br.sasw.esocial_novo.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Properties;


@Configuration
@EnableScheduling
@Data
public class EmailConfig {

    @Value("${email.default-smtp}")
    private String defaultSmtp;

    @Value("${email.default-username}")
    private String defaultUsername;

    @Value("${email.default-password}")
    private String defaultPassword;

    @Value("${email.default-port}")
    private int defaultPort;

    @Value("${email.default-from-email}")
    private String defaultFromEmail;

    @Value("${email.default-from-name}")
    private String defaultFromName;

    @Value("${email.log-base-path}")
    private String logBasePath;

    @Value("${email.scheduler-interval-ms}")
    private long schedulerIntervalMs;

    @Value("${email.log-emails-enabled}")
    private boolean logEmailsEnabled;

    @Bean
    public JavaMailSender javaMailSender() {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        
        mailSender.setHost(defaultSmtp);
        mailSender.setPort(defaultPort);
        mailSender.setUsername(defaultUsername);
        mailSender.setPassword(defaultPassword);

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.ssl.enable", "false");
        props.put("mail.debug", "false");
        props.put("mail.smtp.timeout", "30000");
        props.put("mail.smtp.connectiontimeout", "30000");

        return mailSender;
    }

    public JavaMailSender createCustomMailSender(String smtp, String username, String password, int port) {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        
        mailSender.setHost(smtp);
        mailSender.setPort(port);
        mailSender.setUsername(username);
        mailSender.setPassword(password);

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.ssl.enable", "false");
        props.put("mail.debug", "false");
        props.put("mail.smtp.timeout", "30000");
        props.put("mail.smtp.connectiontimeout", "30000");

        return mailSender;
    }

    public String getLogPath() {
        return logBasePath + java.time.LocalDate.now().toString() + "\\";
    }

    public String getEmailLogFile() {
        return getLogPath() + "log_processamento_emails.txt";
    }

    public String getErrorLogFile() {
        return getLogPath() + "erros_emails.txt";
    }

}
