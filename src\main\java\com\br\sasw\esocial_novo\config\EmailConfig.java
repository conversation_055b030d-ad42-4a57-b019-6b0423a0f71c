package com.br.sasw.esocial_novo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Properties;


@Configuration
@EnableScheduling
@ConfigurationProperties(prefix = "email")
@Data
public class EmailConfig {

    private String defaultSmtp = "smtplw.com.br";
    private String defaultUsername = "sasw";
    private String defaultPassword = "xNiadJEj9607";
    private int defaultPort = 587;
    private String defaultFromEmail = "<EMAIL>";
    private String defaultFromName = "SASw";

    private long schedulerIntervalMs = 30000;
    private boolean schedulerEnabled = true;

    private String anexoBasePath = "C:\\xampp\\htdocs\\satmobile\\documentos\\anexo-email\\";
    private long maxAnexoSizeMb = 25;

    private String logBasePath = "C:\\SatelliteServer\\log\\";
    private boolean logEmailsEnabled = true;

    private int maxRetryAttempts = 3;
    private long retryDelayMs = 5000;

    private int batchSize = 10;
    private boolean processInBatches = true;

    @Bean
    public JavaMailSender javaMailSender() {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        
        mailSender.setHost(defaultSmtp);
        mailSender.setPort(defaultPort);
        mailSender.setUsername(defaultUsername);
        mailSender.setPassword(defaultPassword);

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.ssl.enable", "false");
        props.put("mail.debug", "false");
        props.put("mail.smtp.timeout", "30000");
        props.put("mail.smtp.connectiontimeout", "30000");

        return mailSender;
    }

    public JavaMailSender createCustomMailSender(String smtp, String username, String password, int port) {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        
        mailSender.setHost(smtp);
        mailSender.setPort(port);
        mailSender.setUsername(username);
        mailSender.setPassword(password);

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.ssl.enable", "false");
        props.put("mail.debug", "false");
        props.put("mail.smtp.timeout", "30000");
        props.put("mail.smtp.connectiontimeout", "30000");

        return mailSender;
    }

    public String getLogPath() {
        return logBasePath + java.time.LocalDate.now().toString() + "\\";
    }

    public String getEmailLogFile() {
        return getLogPath() + "log_processamento_emails.txt";
    }

    public String getErrorLogFile() {
        return getLogPath() + "erros_emails.txt";
    }

}
