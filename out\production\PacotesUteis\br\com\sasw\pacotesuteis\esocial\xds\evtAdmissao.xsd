﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtAdmissao/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtAdmissao/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtAdmissao">
                    <xs:annotation>
                        <xs:documentation>Evento Cadastramento Inicial do Vinculo e Admissao / Ingresso de Trabalhador</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeEveTrab">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="trabalhador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes Pessoais do Trabalhador</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="cpfTrab">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>CPF do trabalhador</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:length value="11"/>
                                                    <xs:pattern value="\d{11}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nisTrab">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>NIS</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="1"/>
                                                    <xs:maxLength value="11"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nmTrab">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Nome do Trabalhador</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="2"/>
                                                    <xs:maxLength value="70"/>
                                                    <xs:whiteSpace value="preserve"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="sexo">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Sexo</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="[F|M]"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="racaCor">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Raca e cor</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="estCiv" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Estado civil do trabalhador</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="grauInstr">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Grau de instrucao do trabalhador</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="\d{2}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="indPriEmpr" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Primeiro Emprego</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="[N|S]"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nmSoc" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Nome Social</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="2"/>
                                                    <xs:maxLength value="70"/>
                                                    <xs:whiteSpace value="preserve"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nascimento">
                                            <xs:annotation>
                                                <xs:documentation>Grupo de informacoes do nascimento do trabalhador</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="dtNascto">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Data de Nascimento</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:date">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="codMunic" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo do Municipio</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:integer">
                                                                <xs:pattern value="\d{7}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="uf" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Sigla da UF</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:enumeration value="AC"/>
                                                                <xs:enumeration value="AL"/>
                                                                <xs:enumeration value="AP"/>
                                                                <xs:enumeration value="AM"/>
                                                                <xs:enumeration value="BA"/>
                                                                <xs:enumeration value="CE"/>
                                                                <xs:enumeration value="DF"/>
                                                                <xs:enumeration value="ES"/>
                                                                <xs:enumeration value="GO"/>
                                                                <xs:enumeration value="MA"/>
                                                                <xs:enumeration value="MT"/>
                                                                <xs:enumeration value="MS"/>
                                                                <xs:enumeration value="MG"/>
                                                                <xs:enumeration value="PA"/>
                                                                <xs:enumeration value="PB"/>
                                                                <xs:enumeration value="PR"/>
                                                                <xs:enumeration value="PE"/>
                                                                <xs:enumeration value="PI"/>
                                                                <xs:enumeration value="RJ"/>
                                                                <xs:enumeration value="RN"/>
                                                                <xs:enumeration value="RS"/>
                                                                <xs:enumeration value="RO"/>
                                                                <xs:enumeration value="RR"/>
                                                                <xs:enumeration value="SC"/>
                                                                <xs:enumeration value="SP"/>
                                                                <xs:enumeration value="SE"/>
                                                                <xs:enumeration value="TO"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="paisNascto">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo do Pais de Nascimento</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:length value="3"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="paisNac">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo do pais de nacionalidade do trabalhador</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:length value="3"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="nmMae" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Nome da mae do trabalhador</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="2"/>
                                                                <xs:maxLength value="70"/>
                                                                <xs:whiteSpace value="preserve"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="nmPai" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Nome do pai do trabalhador</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="2"/>
                                                                <xs:maxLength value="70"/>
                                                                <xs:whiteSpace value="preserve"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="documentos" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes dos documentos pessoais do trabalhador</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="CTPS" type="TCtps" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Carteira de Trabalho e Previdencia Social</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="RIC" type="TRic" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes do Documento Nacional de Identidade (DNI)</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="RG" type="TRg" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes do Registro Geral (RG)</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="RNE" type="TRne" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes do Registro Nacional de Estrangeiro</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="OC" type="TOc" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes do número de registro em Orgao de Classe (OC)</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="CNH" type="TCnh" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes da Carteira Nacional de Habilitacao (CNH)</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="endereco">
                                            <xs:annotation>
                                                <xs:documentation>Endereco do Trabalhador</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:choice>
                                                        <xs:element name="brasil" type="TEnderecoBrasil">
                                                            <xs:annotation>
                                                                <xs:documentation>Endereco no Brasil</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                        <xs:element name="exterior" type="TEnderecoExterior">
                                                            <xs:annotation>
                                                                <xs:documentation>Endereco no Exterior</xs:documentation>
                                                            </xs:annotation>
                                                        </xs:element>
                                                    </xs:choice>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="trabEstrangeiro" type="TTrabEstrang" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes do Trabalhador Estrangeiro</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="infoDeficiencia" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Pessoa com Deficiencia</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="defFisica">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Deficiencia Fisica</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="[N|S]"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="defVisual">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Deficiencia visual</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="[N|S]"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="defAuditiva">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Deficiencia auditiva</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="[N|S]"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="defMental">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Deficiencia Mental</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="[N|S]"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="defIntelectual">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Deficiencia Intelectual</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="[N|S]"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="reabReadap">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Reabilitado?</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="[N|S]"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="infoCota">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Informar se o trabalhador preenche cota PCD</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="[N|S]"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="observacao" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Observacao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="2"/>
                                                                <xs:maxLength value="255"/>
                                                                <xs:whiteSpace value="preserve"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="dependente" type="TDependente" minOccurs="0" maxOccurs="99">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes dos dependentes</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="aposentadoria" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacao de aposentadoria do trabalhador</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="trabAposent">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Informar se o trabalhador ja recebe o beneficio previdenciario da aposentadoria por tempo de contribuicao ou por idade</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="[N|S]"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="contato" type="TContato" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes de Contato</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="vinculo">
                                <xs:annotation>
                                    <xs:documentation>Informacoes do Vinculo</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="matricula">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Matricula</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="1"/>
                                                    <xs:maxLength value="30"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="tpRegTrab">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Regime trabalhista</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="tpRegPrev">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Regime previdenciario</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nrRecInfPrelim" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Recibo do evento S-2190</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="1"/>
                                                    <xs:maxLength value="40"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="cadIni">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Indicativo de cadastramento inicial</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="[N|S]"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="infoRegimeTrab">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes do regime trabalhista</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:choice>
                                                        <xs:element name="infoCeletista">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes de Trabalhador Celetista</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:complexType>
                                                                <xs:sequence>
                                                                    <xs:element name="dtAdm">
                                                                        <xs:simpleType>
                                                                            <xs:annotation>
                                                                                <xs:documentation>Data de admissao do trabalhador</xs:documentation>
                                                                            </xs:annotation>
                                                                            <xs:restriction base="xs:date">
                                                                            </xs:restriction>
                                                                        </xs:simpleType>
                                                                    </xs:element>
                                                                    <xs:element name="tpAdmissao">
                                                                        <xs:simpleType>
                                                                            <xs:annotation>
                                                                                <xs:documentation>Tipo de admissao do trabalhador</xs:documentation>
                                                                            </xs:annotation>
                                                                            <xs:restriction base="xs:byte">
                                                                                <xs:pattern value="\d"/>
                                                                            </xs:restriction>
                                                                        </xs:simpleType>
                                                                    </xs:element>
                                                                    <xs:element name="indAdmissao">
                                                                        <xs:simpleType>
                                                                            <xs:annotation>
                                                                                <xs:documentation>Indicativo de Admissao</xs:documentation>
                                                                            </xs:annotation>
                                                                            <xs:restriction base="xs:byte">
                                                                                <xs:pattern value="\d"/>
                                                                            </xs:restriction>
                                                                        </xs:simpleType>
                                                                    </xs:element>
                                                                    <xs:element name="tpRegJor">
                                                                        <xs:simpleType>
                                                                            <xs:annotation>
                                                                                <xs:documentation>Regime de jornada do empregado</xs:documentation>
                                                                            </xs:annotation>
                                                                            <xs:restriction base="xs:byte">
                                                                                <xs:pattern value="\d"/>
                                                                            </xs:restriction>
                                                                        </xs:simpleType>
                                                                    </xs:element>
                                                                    <xs:element name="natAtividade">
                                                                        <xs:simpleType>
                                                                            <xs:annotation>
                                                                                <xs:documentation>Natureza da atividade</xs:documentation>
                                                                            </xs:annotation>
                                                                            <xs:restriction base="xs:byte">
                                                                                <xs:pattern value="\d"/>
                                                                            </xs:restriction>
                                                                        </xs:simpleType>
                                                                    </xs:element>
                                                                    <xs:element name="dtBase" minOccurs="0">
                                                                        <xs:simpleType>
                                                                            <xs:annotation>
                                                                                <xs:documentation>Mes relativo à data base da categoria profissional do trabalhador</xs:documentation>
                                                                            </xs:annotation>
                                                                            <xs:restriction base="xs:byte">
                                                                                <xs:pattern value="\d{1,2}"/>
                                                                            </xs:restriction>
                                                                        </xs:simpleType>
                                                                    </xs:element>
                                                                    <xs:element name="cnpjSindCategProf">
                                                                        <xs:simpleType>
                                                                            <xs:annotation>
                                                                                <xs:documentation>Preencher com o CNPJ do sindicato representativo da categoria (Preponderante ou Diferenciada)</xs:documentation>
                                                                            </xs:annotation>
                                                                            <xs:restriction base="xs:string">
                                                                                <xs:length value="14"/>
                                                                                <xs:pattern value="\d{14}"/>
                                                                            </xs:restriction>
                                                                        </xs:simpleType>
                                                                    </xs:element>
                                                                    <xs:element name="FGTS" type="TFgts">
                                                                        <xs:annotation>
                                                                            <xs:documentation>Informacoes do Fundo de Garantia do Tempo de Servico - FGTS</xs:documentation>
                                                                        </xs:annotation>
                                                                    </xs:element>
                                                                    <xs:element name="trabTemporario" minOccurs="0">
                                                                        <xs:annotation>
                                                                            <xs:documentation>Dados sobre trabalho temporario</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:complexType>
                                                                            <xs:sequence>
                                                                                <xs:element name="hipLeg">
                                                                                    <xs:simpleType>
                                                                                        <xs:annotation>
                                                                                            <xs:documentation>Motivo da contratacao de temporario</xs:documentation>
                                                                                        </xs:annotation>
                                                                                        <xs:restriction base="xs:byte">
                                                                                            <xs:pattern value="\d"/>
                                                                                        </xs:restriction>
                                                                                    </xs:simpleType>
                                                                                </xs:element>
                                                                                <xs:element name="justContr">
                                                                                    <xs:simpleType>
                                                                                        <xs:annotation>
                                                                                            <xs:documentation>Justificativa da hipOtese legal</xs:documentation>
                                                                                        </xs:annotation>
                                                                                        <xs:restriction base="xs:string">
                                                                                            <xs:minLength value="2"/>
                                                                                            <xs:maxLength value="999"/>
                                                                                            <xs:whiteSpace value="preserve"/>
                                                                                        </xs:restriction>
                                                                                    </xs:simpleType>
                                                                                </xs:element>
                                                                                <xs:element name="tpInclContr">
                                                                                    <xs:simpleType>
                                                                                        <xs:annotation>
                                                                                            <xs:documentation>Tipo de inclusao de contrato</xs:documentation>
                                                                                        </xs:annotation>
                                                                                        <xs:restriction base="xs:byte">
                                                                                            <xs:pattern value="\d"/>
                                                                                        </xs:restriction>
                                                                                    </xs:simpleType>
                                                                                </xs:element>
                                                                                <xs:element name="ideTomadorServ">
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Identifica a empresa contratante para a qual o trabalhador temporario sera alocado </xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:complexType>
                                                                                        <xs:sequence>
                                                                                            <xs:element name="tpInsc">
                                                                                                <xs:simpleType>
                                                                                                    <xs:annotation>
                                                                                                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                                                                    </xs:annotation>
                                                                                                    <xs:restriction base="xs:byte">
                                                                                                        <xs:pattern value="\d"/>
                                                                                                    </xs:restriction>
                                                                                                </xs:simpleType>
                                                                                            </xs:element>
                                                                                            <xs:element name="nrInsc">
                                                                                                <xs:simpleType>
                                                                                                    <xs:annotation>
                                                                                                        <xs:documentation>Número de Inscricao</xs:documentation>
                                                                                                    </xs:annotation>
                                                                                                    <xs:restriction base="xs:string">
                                                                                                        <xs:pattern value="\d{8,14}"/>
                                                                                                    </xs:restriction>
                                                                                                </xs:simpleType>
                                                                                            </xs:element>
                                                                                            <xs:element name="ideEstabVinc" minOccurs="0">
                                                                                                <xs:annotation>
                                                                                                    <xs:documentation>Identificacao do estabelecimento ao qual o trabalhador esta vinculado</xs:documentation>
                                                                                                </xs:annotation>
                                                                                                <xs:complexType>
                                                                                                    <xs:sequence>
                                                                                                        <xs:element name="tpInsc">
                                                                                                            <xs:simpleType>
                                                                                                                <xs:annotation>
                                                                                                                    <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                                                                                </xs:annotation>
                                                                                                                <xs:restriction base="xs:byte">
                                                                                                                    <xs:pattern value="\d"/>
                                                                                                                </xs:restriction>
                                                                                                            </xs:simpleType>
                                                                                                        </xs:element>
                                                                                                        <xs:element name="nrInsc">
                                                                                                            <xs:simpleType>
                                                                                                                <xs:annotation>
                                                                                                                    <xs:documentation>Número de Inscricao</xs:documentation>
                                                                                                                </xs:annotation>
                                                                                                                <xs:restriction base="xs:string">
                                                                                                                    <xs:pattern value="\d{8,14}"/>
                                                                                                                </xs:restriction>
                                                                                                            </xs:simpleType>
                                                                                                        </xs:element>
                                                                                                    </xs:sequence>
                                                                                                </xs:complexType>
                                                                                            </xs:element>
                                                                                        </xs:sequence>
                                                                                    </xs:complexType>
                                                                                </xs:element>
                                                                                <xs:element name="ideTrabSubstituido" minOccurs="0" maxOccurs="9">
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Identificacao do(s) trabalhador(es) substituido(s)</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:complexType>
                                                                                        <xs:sequence>
                                                                                            <xs:element name="cpfTrabSubst">
                                                                                                <xs:simpleType>
                                                                                                    <xs:annotation>
                                                                                                        <xs:documentation>Cpf do trabalhador substituido</xs:documentation>
                                                                                                    </xs:annotation>
                                                                                                    <xs:restriction base="xs:string">
                                                                                                        <xs:length value="11"/>
                                                                                                        <xs:pattern value="\d{11}"/>
                                                                                                    </xs:restriction>
                                                                                                </xs:simpleType>
                                                                                            </xs:element>
                                                                                        </xs:sequence>
                                                                                    </xs:complexType>
                                                                                </xs:element>
                                                                            </xs:sequence>
                                                                        </xs:complexType>
                                                                    </xs:element>
                                                                    <xs:element name="aprend" minOccurs="0">
                                                                        <xs:annotation>
                                                                            <xs:documentation>Informacoes relacionadas ao aprendiz</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:complexType>
                                                                            <xs:sequence>
                                                                                <xs:element name="tpInsc">
                                                                                    <xs:simpleType>
                                                                                        <xs:annotation>
                                                                                            <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                                                        </xs:annotation>
                                                                                        <xs:restriction base="xs:byte">
                                                                                            <xs:pattern value="\d"/>
                                                                                        </xs:restriction>
                                                                                    </xs:simpleType>
                                                                                </xs:element>
                                                                                <xs:element name="nrInsc">
                                                                                    <xs:simpleType>
                                                                                        <xs:annotation>
                                                                                            <xs:documentation>Número de Inscricao</xs:documentation>
                                                                                        </xs:annotation>
                                                                                        <xs:restriction base="xs:string">
                                                                                            <xs:pattern value="\d{8,14}"/>
                                                                                        </xs:restriction>
                                                                                    </xs:simpleType>
                                                                                </xs:element>
                                                                            </xs:sequence>
                                                                        </xs:complexType>
                                                                    </xs:element>
                                                                </xs:sequence>
                                                            </xs:complexType>
                                                        </xs:element>
                                                        <xs:element name="infoEstatutario">
                                                            <xs:annotation>
                                                                <xs:documentation>Informacoes de Trabalhador Estatutario</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:complexType>
                                                                <xs:sequence>
                                                                    <xs:element name="indProvim">
                                                                        <xs:simpleType>
                                                                            <xs:annotation>
                                                                                <xs:documentation>Indicativo de provimento</xs:documentation>
                                                                            </xs:annotation>
                                                                            <xs:restriction base="xs:byte">
                                                                                <xs:pattern value="\d"/>
                                                                            </xs:restriction>
                                                                        </xs:simpleType>
                                                                    </xs:element>
                                                                    <xs:element name="tpProv">
                                                                        <xs:simpleType>
                                                                            <xs:annotation>
                                                                                <xs:documentation>Preencher com o tipo de provimento</xs:documentation>
                                                                            </xs:annotation>
                                                                            <xs:restriction base="xs:byte">
                                                                                <xs:pattern value="\d{1,2}"/>
                                                                            </xs:restriction>
                                                                        </xs:simpleType>
                                                                    </xs:element>
                                                                    <xs:element name="dtNomeacao">
                                                                        <xs:simpleType>
                                                                            <xs:annotation>
                                                                                <xs:documentation>Preencher com a data da nomeacao do servidor</xs:documentation>
                                                                            </xs:annotation>
                                                                            <xs:restriction base="xs:date">
                                                                            </xs:restriction>
                                                                        </xs:simpleType>
                                                                    </xs:element>
                                                                    <xs:element name="dtPosse">
                                                                        <xs:simpleType>
                                                                            <xs:annotation>
                                                                                <xs:documentation>Data da posse do servidor</xs:documentation>
                                                                            </xs:annotation>
                                                                            <xs:restriction base="xs:date">
                                                                            </xs:restriction>
                                                                        </xs:simpleType>
                                                                    </xs:element>
                                                                    <xs:element name="dtExercicio">
                                                                        <xs:simpleType>
                                                                            <xs:annotation>
                                                                                <xs:documentation>Data da entrada em exercicio pelo servidor</xs:documentation>
                                                                            </xs:annotation>
                                                                            <xs:restriction base="xs:date">
                                                                            </xs:restriction>
                                                                        </xs:simpleType>
                                                                    </xs:element>
                                                                    <xs:element name="tpPlanRP" minOccurs="0">
                                                                        <xs:simpleType>
                                                                            <xs:annotation>
                                                                                <xs:documentation>Tipo de plano de segregacao da massa</xs:documentation>
                                                                            </xs:annotation>
                                                                            <xs:restriction base="xs:byte">
                                                                                <xs:pattern value="\d"/>
                                                                            </xs:restriction>
                                                                        </xs:simpleType>
                                                                    </xs:element>
                                                                    <xs:element name="infoDecJud" minOccurs="0">
                                                                        <xs:annotation>
                                                                            <xs:documentation>Informacoes sobre os dados da decisao judicial</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:complexType>
                                                                            <xs:sequence>
                                                                                <xs:element name="nrProcJud">
                                                                                    <xs:simpleType>
                                                                                        <xs:annotation>
                                                                                            <xs:documentation>Nr Processo Judicial</xs:documentation>
                                                                                        </xs:annotation>
                                                                                        <xs:restriction base="xs:string">
                                                                                            <xs:length value="20"/>
                                                                                        </xs:restriction>
                                                                                    </xs:simpleType>
                                                                                </xs:element>
                                                                            </xs:sequence>
                                                                        </xs:complexType>
                                                                    </xs:element>
                                                                </xs:sequence>
                                                            </xs:complexType>
                                                        </xs:element>
                                                    </xs:choice>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="infoContrato">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes do Contrato de Trabalho</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="codCargo" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo do Cargo</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="1"/>
                                                                <xs:maxLength value="30"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="codFuncao" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo da Funcao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="1"/>
                                                                <xs:maxLength value="30"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="codCateg">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo da Categoria</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:integer">
                                                                <xs:pattern value="\d{3}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="codCarreira" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo da Carreira</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="1"/>
                                                                <xs:maxLength value="30"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="dtIngrCarr" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Data de ingresso na carreira</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:date">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="remuneracao" type="TRemun">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes da remuneracao e periodicidade de pagamento</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="duracao">
                                                        <xs:annotation>
                                                            <xs:documentation>Duracao do Contrato de Trabalho</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="tpContr">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Tipo de contrato</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dtTerm" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Data do Término</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:date">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="clauAssec" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Indicativo de clausula assecuratOria</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:pattern value="[N|S]"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="objDet" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Indicacao do objeto determinante da contratacao por prazo determinado</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="2"/>
                                                                            <xs:maxLength value="255"/>
                                                                            <xs:whiteSpace value="preserve"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="localTrabalho">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes do local de trabalho</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="localTrabGeral" type="TLocalTrab" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Estabelecimento onde o trabalhador exercera suas atividades</xs:documentation>
                                                                    </xs:annotation>
                                                                </xs:element>
                                                                <xs:element name="localTrabDom" type="TEnderecoBrasil" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Endereco de trabalho do trabalhador doméstico e trabalhador temporario</xs:documentation>
                                                                    </xs:annotation>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="horContratual" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes do Horario Contratual do Trabalhador</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="qtdHrsSem" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Quantidade média de horas relativas à jornada semanal do trabalhador</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="4"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="99.99"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="tpJornada">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Tipo da Jornada</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dscTpJorn" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Descricao do tipo de jornada</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="100"/>
                                                                            <xs:whiteSpace value="preserve"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="tmpParc">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Contrato de trabalho em regime de tempo parcial</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="horario" type="THorario" minOccurs="0" maxOccurs="99">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Informacoes diarias do horario contratual</xs:documentation>
                                                                    </xs:annotation>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="filiacaoSindical" minOccurs="0" maxOccurs="2">
                                                        <xs:annotation>
                                                            <xs:documentation>Filiacao Sindical do Trabalhador</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="cnpjSindTrab">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>CNPJ do Sindicato</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:length value="14"/>
                                                                            <xs:pattern value="\d{14}"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="alvaraJudicial" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Dados do Alvara Judicial</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="nrProcJud">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Identificacao do Número do Processo Judicial</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:length value="20"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="observacoes" minOccurs="0" maxOccurs="99">
                                                        <xs:annotation>
                                                            <xs:documentation>Observacoes do contrato de trabalho</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="observacao">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Observacao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="2"/>
                                                                            <xs:maxLength value="255"/>
                                                                            <xs:whiteSpace value="preserve"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="sucessaoVinc" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Grupo de informacoes da sucessao de vinculo trabalhista</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="tpInscAnt">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Tipo de inscricao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:byte">
                                                                <xs:pattern value="\d"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="cnpjEmpregAnt">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>CNPJ do empregador anterior</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="\d{8,14}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="matricAnt" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Matricula no empregador anterior</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="1"/>
                                                                <xs:maxLength value="30"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="dtTransf">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Data da transferencia</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:date">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="observacao" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Observacao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="2"/>
                                                                <xs:maxLength value="255"/>
                                                                <xs:whiteSpace value="preserve"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="transfDom" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes do empregado doméstico transferido de outro representante da mesma unidade familiar</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="cpfSubstituido">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>CPF do representante anterior</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="\d{11}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="matricAnt" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Matricula no empregador anterior</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="1"/>
                                                                <xs:maxLength value="30"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="dtTransf">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Data da transferencia</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:date">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="mudancaCPF" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes de mudanca de CPF do trabalhador</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="cpfAnt">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>CPF anterior</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:pattern value="\d{11}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="matricAnt">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Matricula no empregador anterior</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="1"/>
                                                                <xs:maxLength value="30"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="dtAltCPF">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Data de alteracao do CPF</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:date">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="observacao" minOccurs="0">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Observacao</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:minLength value="2"/>
                                                                <xs:maxLength value="255"/>
                                                                <xs:whiteSpace value="preserve"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="afastamento" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes de afastamento do trabalhador</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="dtIniAfast">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Data de inicio do afastamento</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:date">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                    <xs:element name="codMotAfast">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>COdigo do motivo de afastamento temporario, conforme tabela 18</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:string">
                                                                <xs:length value="2"/>
                                                                <xs:pattern value="\d{2}"/>
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="desligamento" minOccurs="0">
                                            <xs:annotation>
                                                <xs:documentation>Informacoes de desligamento do trabalhador</xs:documentation>
                                            </xs:annotation>
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="dtDeslig">
                                                        <xs:simpleType>
                                                            <xs:annotation>
                                                                <xs:documentation>Data do Desligamento</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:restriction base="xs:date">
                                                            </xs:restriction>
                                                        </xs:simpleType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeEveTrab">
        <xs:annotation>
            <xs:documentation>Identificacao do evento</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="indRetif">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Retificacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrRecibo" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do recibo do arquivo a ser retificado</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="40"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TCtps">
        <xs:annotation>
            <xs:documentation>Carteira de Trabalho e Previdencia Social</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="nrCtps">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número da CTPS</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="11"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="serieCtps">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de série da CTPS</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="5"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="ufCtps">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>UF da expedicao da CTPS</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="AC"/>
                        <xs:enumeration value="AL"/>
                        <xs:enumeration value="AP"/>
                        <xs:enumeration value="AM"/>
                        <xs:enumeration value="BA"/>
                        <xs:enumeration value="CE"/>
                        <xs:enumeration value="DF"/>
                        <xs:enumeration value="ES"/>
                        <xs:enumeration value="GO"/>
                        <xs:enumeration value="MA"/>
                        <xs:enumeration value="MT"/>
                        <xs:enumeration value="MS"/>
                        <xs:enumeration value="MG"/>
                        <xs:enumeration value="PA"/>
                        <xs:enumeration value="PB"/>
                        <xs:enumeration value="PR"/>
                        <xs:enumeration value="PE"/>
                        <xs:enumeration value="PI"/>
                        <xs:enumeration value="RJ"/>
                        <xs:enumeration value="RN"/>
                        <xs:enumeration value="RS"/>
                        <xs:enumeration value="RO"/>
                        <xs:enumeration value="RR"/>
                        <xs:enumeration value="SC"/>
                        <xs:enumeration value="SP"/>
                        <xs:enumeration value="SE"/>
                        <xs:enumeration value="TO"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TRic">
        <xs:annotation>
            <xs:documentation>Registro de Identificacao Civil</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="nrRic">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>N° do DIC</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="14"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="orgaoEmissor">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Orgao e UF de emissao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="20"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dtExped" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Data da expedicao do documento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TRg">
        <xs:sequence>
            <xs:element name="nrRg">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do RG</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="14"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="orgaoEmissor">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Orgao e UF de emissao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="20"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dtExped" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Data da expedicao do documento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TRne">
        <xs:annotation>
            <xs:documentation>Registro Nacional de Estrangeiros</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="nrRne">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>RNE</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="14"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="orgaoEmissor">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Orgao e UF de emissao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="20"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dtExped" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Data da expedicao do documento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TOc">
        <xs:annotation>
            <xs:documentation>Orgao de Classe</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="nrOc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número Inscricao OC</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="14"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="orgaoEmissor">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Orgao e UF de emissao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="20"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dtExped" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Data da expedicao do documento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dtValid" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Data de validade do documento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TCnh">
        <xs:annotation>
            <xs:documentation>Cartera Nacional de Habilitacao</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="nrRegCnh">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do Registro da Carteira Nacional de Habilitacao - CNH</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="3"/>
                        <xs:maxLength value="12"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dtExped" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Data da expedicao do documento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="ufCnh">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Estado da Federacao emissor da CNH</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="AC"/>
                        <xs:enumeration value="AL"/>
                        <xs:enumeration value="AP"/>
                        <xs:enumeration value="AM"/>
                        <xs:enumeration value="BA"/>
                        <xs:enumeration value="CE"/>
                        <xs:enumeration value="DF"/>
                        <xs:enumeration value="ES"/>
                        <xs:enumeration value="GO"/>
                        <xs:enumeration value="MA"/>
                        <xs:enumeration value="MT"/>
                        <xs:enumeration value="MS"/>
                        <xs:enumeration value="MG"/>
                        <xs:enumeration value="PA"/>
                        <xs:enumeration value="PB"/>
                        <xs:enumeration value="PR"/>
                        <xs:enumeration value="PE"/>
                        <xs:enumeration value="PI"/>
                        <xs:enumeration value="RJ"/>
                        <xs:enumeration value="RN"/>
                        <xs:enumeration value="RS"/>
                        <xs:enumeration value="RO"/>
                        <xs:enumeration value="RR"/>
                        <xs:enumeration value="SC"/>
                        <xs:enumeration value="SP"/>
                        <xs:enumeration value="SE"/>
                        <xs:enumeration value="TO"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dtValid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Data de validade do documento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dtPriHab" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Data da primeira habilitacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="categoriaCnh">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Categoria da CNH</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\w{1,2}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEnderecoBrasil">
        <xs:annotation>
            <xs:documentation>Informacoes do Endereco no Brasil</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpLograd">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="4"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dscLograd">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Descricao do logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="100"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrLograd">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="10"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="complemento" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Complemento do logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="30"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="bairro" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Nome do bairro/distrito</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="90"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="cep">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo de Enderecamento Postal - CEP</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="codMunic">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo do Municipio</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:integer">
                        <xs:pattern value="\d{7}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="uf">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Sigla da UF</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="AC"/>
                        <xs:enumeration value="AL"/>
                        <xs:enumeration value="AP"/>
                        <xs:enumeration value="AM"/>
                        <xs:enumeration value="BA"/>
                        <xs:enumeration value="CE"/>
                        <xs:enumeration value="DF"/>
                        <xs:enumeration value="ES"/>
                        <xs:enumeration value="GO"/>
                        <xs:enumeration value="MA"/>
                        <xs:enumeration value="MT"/>
                        <xs:enumeration value="MS"/>
                        <xs:enumeration value="MG"/>
                        <xs:enumeration value="PA"/>
                        <xs:enumeration value="PB"/>
                        <xs:enumeration value="PR"/>
                        <xs:enumeration value="PE"/>
                        <xs:enumeration value="PI"/>
                        <xs:enumeration value="RJ"/>
                        <xs:enumeration value="RN"/>
                        <xs:enumeration value="RS"/>
                        <xs:enumeration value="RO"/>
                        <xs:enumeration value="RR"/>
                        <xs:enumeration value="SC"/>
                        <xs:enumeration value="SP"/>
                        <xs:enumeration value="SE"/>
                        <xs:enumeration value="TO"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEnderecoExterior">
        <xs:annotation>
            <xs:documentation>Informacoes do Endereco no Exterior</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="paisResid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo do Pais</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:length value="3"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dscLograd">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Descricao do logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="100"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrLograd">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="10"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="complemento" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Complemento do logradouro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="30"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="bairro" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Nome do bairro/distrito</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="90"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nmCid">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Nome da Cidade</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="50"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="codPostal" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo de Enderecamento Postal</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="4"/>
                        <xs:maxLength value="12"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TTrabEstrang">
        <xs:annotation>
            <xs:documentation>Informacoes do Trabalhador Estrangeiro</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="dtChegada" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Data de chegada ao Brasil</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="classTrabEstrang">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Classificacao da condicao do estrangeiro</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d{1,2}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="casadoBr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Casado com brasileiro(a): 
                            S - Sim;
                            N - Nao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[N|S]"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="filhosBr">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicar se o trabalhador estrangeiro tem filhos brasileiros:
                            S - Sim;
                            N - Nao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[N|S]"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TDependente">
        <xs:sequence>
            <xs:element name="tpDep">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de dependente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:length value="2"/>
                        <xs:pattern value="\d{2}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nmDep">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Nome do Dependente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="70"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dtNascto">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Data de Nascimento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="cpfDep" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao no CPF</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{11}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="depIRRF">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Dependente para fins de IRRF</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[N|S]"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="depSF">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Dependente SF</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[N|S]"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="incTrab">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Incapacidade para o trabalho</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[N|S]"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TContato">
        <xs:annotation>
            <xs:documentation>Informacoes de Contato</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="fonePrinc" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de telefone do trabalhador</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="8"/>
                        <xs:maxLength value="13"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="foneAlternat" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de telefone alternativo do trabalhador</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="8"/>
                        <xs:maxLength value="13"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="emailPrinc" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Endereco eletrônico</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="6"/>
                        <xs:maxLength value="60"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="emailAlternat" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Endereco eletrônico alternativo</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="6"/>
                        <xs:maxLength value="60"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TFgts">
        <xs:annotation>
            <xs:documentation>Informacoes do FGTS</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="opcFGTS">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Opcao pelo FGTS</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dtOpcFGTS" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Data de Opcao pelo FGTS</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:date">
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TRemun">
        <xs:annotation>
            <xs:documentation>Remuneracao e periodicidade de pagamento</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="vrSalFx">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Salario base do trabalhador</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="14"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="999999999999"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="undSalFixo">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Unidade de pagamento da parte fixa da remuneracao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dscSalVar" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Descricao do salario variavel e como este é calculado  ex: Comissoes pagas no percentual de 10% sobre as vendas</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="255"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TLocalTrab">
        <xs:annotation>
            <xs:documentation>Informacoes do Local de Trabalho</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="descComp" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Descricao complementar</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="80"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="THorario">
        <xs:annotation>
            <xs:documentation>Informacoes de Horario Contratual</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="dia">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indica o dia do horario</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="codHorContrat">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>COdigo do Horario Contratual</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="30"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
