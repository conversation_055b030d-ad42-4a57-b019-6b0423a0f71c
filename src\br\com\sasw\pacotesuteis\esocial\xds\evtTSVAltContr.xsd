﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtTSVAltContr/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtTSVAltContr/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtTSVAltContr">
                    <xs:annotation>
                        <xs:documentation>TSVE - Alteracao Contratual</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeEveTrab">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de Identificacao do Evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideTrabSemVinculo">
                                <xs:annotation>
                                    <xs:documentation>Identificacao do Trabalhador Sem Vinculo de Emprego</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="cpfTrab">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>CPF do trabalhador</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:length value="11"/>
                                                    <xs:pattern value="\d{11}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nisTrab" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>NIS</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:maxLength value="11"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="codCateg">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>COdigo da Categoria</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:integer">
                                                    <xs:pattern value="\d{3}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="infoTSVAlteracao">
                                <xs:annotation>
                                    <xs:documentation>Trabalhador Sem Vinculo de Emprego - Alteracao Contratual</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="dtAlteracao">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Data da Alteracao</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:date">
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="natAtividade" minOccurs="0">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Natureza da atividade</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="infoComplementares" minOccurs="0">
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="cargoFuncao" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Cargo/Funcao ocupado pelo Trabalhador Sem Vinculo</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="codCargo">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo do Cargo</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="30"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="codFuncao" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>COdigo da Funcao</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="30"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                    <xs:element name="remuneracao" type="TRemun" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>Informacoes da remuneracao e periodicidade de pagamento</xs:documentation>
                                                        </xs:annotation>
                                                    </xs:element>
                                                    <xs:element name="infoEstagiario" minOccurs="0">
                                                        <xs:annotation>
                                                            <xs:documentation>informacoes do estagiario</xs:documentation>
                                                        </xs:annotation>
                                                        <xs:complexType>
                                                            <xs:sequence>
                                                                <xs:element name="natEstagio">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Natureza do Estagio:
                                                                                O - ObrigatOrio
                                                                                N - Nao ObrigatOrio</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="1"/>
                                                                            <xs:maxLength value="1"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="nivEstagio">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Informar o nivel do estagio:
                                                                                1 - Fundamental;
                                                                                2 - Médio;
                                                                                3 - Formacao Profissional;
                                                                                4 - Superior</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:byte">
                                                                            <xs:pattern value="\d"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="areaAtuacao" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>area de atuacao do estagiario</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="2"/>
                                                                            <xs:maxLength value="50"/>
                                                                            <xs:whiteSpace value="preserve"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="nrApol" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Nr. ApOlice de Seguro</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:string">
                                                                            <xs:minLength value="2"/>
                                                                            <xs:maxLength value="30"/>
                                                                            <xs:whiteSpace value="preserve"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="vlrBolsa" minOccurs="0">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Valor da Bolsa Estagio</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:decimal">
                                                                            <xs:totalDigits value="14"/>
                                                                            <xs:fractionDigits value="2"/>
                                                                            <xs:maxInclusive value="999999999999"/>
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="dtPrevTerm">
                                                                    <xs:simpleType>
                                                                        <xs:annotation>
                                                                            <xs:documentation>Data prevista para o término do estagio</xs:documentation>
                                                                        </xs:annotation>
                                                                        <xs:restriction base="xs:date">
                                                                        </xs:restriction>
                                                                    </xs:simpleType>
                                                                </xs:element>
                                                                <xs:element name="instEnsino">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Instituicao de Ensino</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="cnpjInstEnsino" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Cnpj da Instituicao de Ensino</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:length value="14"/>
                                                                                        <xs:pattern value="\d{14}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="nmRazao">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Nome/Razao Social</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="100"/>
                                                                                        <xs:whiteSpace value="preserve"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="dscLograd" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Descricao do logradouro</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="100"/>
                                                                                        <xs:whiteSpace value="preserve"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="nrLograd" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Número do logradouro</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="10"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="bairro" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Nome do bairro/distrito</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="90"/>
                                                                                        <xs:whiteSpace value="preserve"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="cep" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>COdigo de Enderecamento Postal - CEP</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:pattern value="\d{8}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="codMunic" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>COdigo do Municipio</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:integer">
                                                                                        <xs:pattern value="\d{7}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="uf" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Sigla da UF</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:enumeration value="AC"/>
                                                                                        <xs:enumeration value="AL"/>
                                                                                        <xs:enumeration value="AP"/>
                                                                                        <xs:enumeration value="AM"/>
                                                                                        <xs:enumeration value="BA"/>
                                                                                        <xs:enumeration value="CE"/>
                                                                                        <xs:enumeration value="DF"/>
                                                                                        <xs:enumeration value="ES"/>
                                                                                        <xs:enumeration value="GO"/>
                                                                                        <xs:enumeration value="MA"/>
                                                                                        <xs:enumeration value="MT"/>
                                                                                        <xs:enumeration value="MS"/>
                                                                                        <xs:enumeration value="MG"/>
                                                                                        <xs:enumeration value="PA"/>
                                                                                        <xs:enumeration value="PB"/>
                                                                                        <xs:enumeration value="PR"/>
                                                                                        <xs:enumeration value="PE"/>
                                                                                        <xs:enumeration value="PI"/>
                                                                                        <xs:enumeration value="RJ"/>
                                                                                        <xs:enumeration value="RN"/>
                                                                                        <xs:enumeration value="RS"/>
                                                                                        <xs:enumeration value="RO"/>
                                                                                        <xs:enumeration value="RR"/>
                                                                                        <xs:enumeration value="SC"/>
                                                                                        <xs:enumeration value="SP"/>
                                                                                        <xs:enumeration value="SE"/>
                                                                                        <xs:enumeration value="TO"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                                <xs:element name="ageIntegracao" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Agente de Integracao</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="cnpjAgntInteg">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>CNPJ do agente de integracao</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:length value="14"/>
                                                                                        <xs:pattern value="\d{14}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="nmRazao">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Nome/Razao Social</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="100"/>
                                                                                        <xs:whiteSpace value="preserve"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="dscLograd">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Descricao do logradouro</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="100"/>
                                                                                        <xs:whiteSpace value="preserve"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="nrLograd">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Número do logradouro</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="10"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="bairro" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Nome do bairro/distrito</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="1"/>
                                                                                        <xs:maxLength value="90"/>
                                                                                        <xs:whiteSpace value="preserve"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="cep">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>COdigo de Enderecamento Postal - CEP</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:pattern value="\d{8}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="codMunic" minOccurs="0">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>COdigo do Municipio</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:integer">
                                                                                        <xs:pattern value="\d{7}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="uf">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Sigla da UF</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:enumeration value="AC"/>
                                                                                        <xs:enumeration value="AL"/>
                                                                                        <xs:enumeration value="AP"/>
                                                                                        <xs:enumeration value="AM"/>
                                                                                        <xs:enumeration value="BA"/>
                                                                                        <xs:enumeration value="CE"/>
                                                                                        <xs:enumeration value="DF"/>
                                                                                        <xs:enumeration value="ES"/>
                                                                                        <xs:enumeration value="GO"/>
                                                                                        <xs:enumeration value="MA"/>
                                                                                        <xs:enumeration value="MT"/>
                                                                                        <xs:enumeration value="MS"/>
                                                                                        <xs:enumeration value="MG"/>
                                                                                        <xs:enumeration value="PA"/>
                                                                                        <xs:enumeration value="PB"/>
                                                                                        <xs:enumeration value="PR"/>
                                                                                        <xs:enumeration value="PE"/>
                                                                                        <xs:enumeration value="PI"/>
                                                                                        <xs:enumeration value="RJ"/>
                                                                                        <xs:enumeration value="RN"/>
                                                                                        <xs:enumeration value="RS"/>
                                                                                        <xs:enumeration value="RO"/>
                                                                                        <xs:enumeration value="RR"/>
                                                                                        <xs:enumeration value="SC"/>
                                                                                        <xs:enumeration value="SP"/>
                                                                                        <xs:enumeration value="SE"/>
                                                                                        <xs:enumeration value="TO"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                                <xs:element name="supervisorEstagio" minOccurs="0">
                                                                    <xs:annotation>
                                                                        <xs:documentation>Supervisor do Estagio</xs:documentation>
                                                                    </xs:annotation>
                                                                    <xs:complexType>
                                                                        <xs:sequence>
                                                                            <xs:element name="cpfSupervisor">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>CPF do responsavel pela supervisao do estagiario</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:length value="11"/>
                                                                                        <xs:pattern value="\d{11}"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                            <xs:element name="nmSuperv">
                                                                                <xs:simpleType>
                                                                                    <xs:annotation>
                                                                                        <xs:documentation>Nome do Supervisor do Estagio</xs:documentation>
                                                                                    </xs:annotation>
                                                                                    <xs:restriction base="xs:string">
                                                                                        <xs:minLength value="2"/>
                                                                                        <xs:maxLength value="70"/>
                                                                                        <xs:whiteSpace value="preserve"/>
                                                                                    </xs:restriction>
                                                                                </xs:simpleType>
                                                                            </xs:element>
                                                                        </xs:sequence>
                                                                    </xs:complexType>
                                                                </xs:element>
                                                            </xs:sequence>
                                                        </xs:complexType>
                                                    </xs:element>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeEveTrab">
        <xs:annotation>
            <xs:documentation>Identificacao do evento</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="indRetif">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Retificacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrRecibo" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do recibo do arquivo a ser retificado</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="40"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TRemun">
        <xs:annotation>
            <xs:documentation>Remuneracao e periodicidade de pagamento</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="vrSalFx">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Salario base do trabalhador</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:decimal">
                        <xs:totalDigits value="14"/>
                        <xs:fractionDigits value="2"/>
                        <xs:maxInclusive value="999999999999"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="undSalFixo">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Unidade de pagamento da parte fixa da remuneracao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="dscSalVar" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Descricao do salario variavel e como este é calculado  ex: Comissoes pagas no percentual de 10% sobre as vendas</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="2"/>
                        <xs:maxLength value="255"/>
                        <xs:whiteSpace value="preserve"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
