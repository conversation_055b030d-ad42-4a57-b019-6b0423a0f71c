package Assinador.XML;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.Certificate;
import java.util.Collections;
import java.util.Enumeration;
import javax.xml.crypto.dsig.CanonicalizationMethod;
import javax.xml.crypto.dsig.DigestMethod;
import javax.xml.crypto.dsig.Reference;
import javax.xml.crypto.dsig.SignatureMethod;
import javax.xml.crypto.dsig.SignedInfo;
import javax.xml.crypto.dsig.Transform;
import javax.xml.crypto.dsig.XMLSignature;
import javax.xml.crypto.dsig.XMLSignatureFactory;
import javax.xml.crypto.dsig.dom.DOMSignContext;
import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import javax.xml.crypto.dsig.keyinfo.KeyInfoFactory;
import javax.xml.crypto.dsig.keyinfo.KeyValue;
import javax.xml.crypto.dsig.spec.C14NMethodParameterSpec;
import javax.xml.crypto.dsig.spec.TransformParameterSpec;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import org.w3c.dom.Document;

public class AssinarXML {

    /**
     * Assina um XML de acordo com os parametros abaixo
     *
     * @param caminhoKeyStore ex: arquivo.p12
     * @param senha
     * @param tipoKeystore ex: TiposKeyStore.PKCS12
     * @param caminhoXML
     * @param idTagParaAssinar
     * @return
     * @throws Exception
     */
    public String assinarXML(String caminhoKeyStore, char[] senha, TiposKeyStore tipoKeystore, String caminhoXML, String idTagParaAssinar) throws Exception {
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        dbf.setNamespaceAware(true);
        DocumentBuilder builder = dbf.newDocumentBuilder();
        Document doc = builder.parse(new FileInputStream(caminhoXML));

        KeyStore ks = KeyStore.getInstance(tipoKeystore.getValue());
        ks.load(new java.io.FileInputStream(caminhoKeyStore), senha);

        PrivateKey chavePrivada = null;
        Certificate chavePublica = null;

        Enumeration<String> enum1 = ks.aliases();

        while (enum1.hasMoreElements()) {
            String alias = enum1.nextElement();
            if (ks.isKeyEntry(alias)) {
                chavePrivada = (PrivateKey) ks.getKey(alias, senha);
                chavePublica = ks.getCertificate(alias);
            }
        }

        DOMSignContext dsc = new DOMSignContext(chavePrivada, doc.getDocumentElement());
        XMLSignatureFactory fac = XMLSignatureFactory.getInstance("DOM");

        Reference ref = fac.newReference("#" + idTagParaAssinar, fac.newDigestMethod(DigestMethod.SHA1, null),
                Collections.singletonList(fac.newTransform(Transform.ENVELOPED,
                        (TransformParameterSpec) null)), null, null);

        SignedInfo si = fac.newSignedInfo(fac.newCanonicalizationMethod(CanonicalizationMethod.INCLUSIVE_WITH_COMMENTS,
                (C14NMethodParameterSpec) null),
                fac.newSignatureMethod(SignatureMethod.RSA_SHA1, null),
                Collections.singletonList(ref));

        KeyInfoFactory kif = fac.getKeyInfoFactory();

        KeyValue kv = kif.newKeyValue(chavePublica.getPublicKey());
        KeyInfo ki = kif.newKeyInfo(Collections.singletonList(kv));

        XMLSignature signature = fac.newXMLSignature(si, ki);

        signature.sign(dsc);

        OutputStream os = new ByteArrayOutputStream();

        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer trans = tf.newTransformer();
        trans.transform(new DOMSource(doc), new StreamResult(os));

        return os.toString();
    }

    /**
     * @param args
     */
    public static void main(String[] args) throws Exception {
        char[] senha = "senha".toCharArray();
        String caminhoKeyStore = "D:/workspace_personal/certificados/arquivo.kdb";
        String caminhoXML = "D:/workspace_personal/certificados/xmls_outros_estados/retorno.xml";
        String idTagParaAssinar = "retornoProcessamento";
        String retornoXML = new AssinarXML().assinarXML(caminhoKeyStore, senha, TiposKeyStore.JKS, caminhoXML, idTagParaAssinar);
        System.out.println(retornoXML);
    }

}
