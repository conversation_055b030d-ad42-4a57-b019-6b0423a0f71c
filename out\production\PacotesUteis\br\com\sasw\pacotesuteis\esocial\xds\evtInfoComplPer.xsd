﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtInfoComplPer/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtInfoComplPer/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtInfoComplPer">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeEveFopag">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="infoSubstPatr" minOccurs="0">
                                <xs:annotation>
                                    <xs:documentation>Inf. Complementares - Empresas Enquadradas nos artigos 7 a 9 da Lei 12.546/2011</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="indSubstPatr">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Substituicao da CP patronal</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="percRedContrib">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Percentual de Reducao da Contribuicao</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:decimal">
                                                    <xs:totalDigits value="5"/>
                                                    <xs:fractionDigits value="2"/>
                                                    <xs:maxInclusive value="100"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="infoSubstPatrOpPort" minOccurs="0" maxOccurs="9999">
                                <xs:annotation>
                                    <xs:documentation>Informacao de substituicao prevista na Lei 12.546/2011</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="cnpjOpPortuario">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>CNPJ do Operador Portuario</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:length value="14"/>
                                                    <xs:pattern value="\d{14}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="infoAtivConcom" minOccurs="0">
                                <xs:annotation>
                                    <xs:documentation>Empresas enquadradas no Simples Nacional - Atividades Concomitantes</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="fatorMes">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Fator a ser utilizado para calculo da contribuicao incidente sobre a remuneracao mensal</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:decimal">
                                                    <xs:totalDigits value="5"/>
                                                    <xs:fractionDigits value="2"/>
                                                    <xs:maxInclusive value="100"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="fator13">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Fator a ser utilizado para calculo da contribuicao incidente sobre a remuneracao relativa ao décimo terceiro salario</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:decimal">
                                                    <xs:totalDigits value="5"/>
                                                    <xs:fractionDigits value="2"/>
                                                    <xs:maxInclusive value="100"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeEveFopag">
        <xs:annotation>
            <xs:documentation>Identificacao do evento periOdico</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="indRetif">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Retificacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrRecibo" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do recibo do arquivo a ser retificado</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="40"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="indApuracao">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de periodo de apuracao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="perApur">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo ao qual se referem as informacoes</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
