# Exemplos de Uso da API de Emails

Este documento contém exemplos práticos de como usar a API REST do sistema de emails.

## 🚀 Iniciando a Aplicação

```bash
# Compilar e executar
mvn clean spring-boot:run

# A aplicação estará disponível em:
# http://localhost:8081/esocial-novo
```

## 📊 Monitoramento e Status

### 1. Verificar Status do Sistema

```bash
curl -X GET "http://localhost:8081/esocial-novo/api/emails/status" \
  -H "Content-Type: application/json"
```

**Resposta:**
```json
{
  "ativo": true,
  "processando": false,
  "intervalMs": 30000,
  "emailStats": {
    "pendentes": 5,
    "enviadosHoje": 12,
    "comFalha": 1
  }
}
```

### 2. Health Check

```bash
curl -X GET "http://localhost:8081/esocial-novo/api/emails/health"
```

**Resposta:**
```json
{
  "status": "UP",
  "scheduler": {
    "ativo": true,
    "processando": false
  },
  "emails": {
    "pendentes": 5
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 3. Estatísticas Detalhadas

```bash
curl -X GET "http://localhost:8081/esocial-novo/api/emails/estatisticas"
```

**Resposta:**
```json
{
  "pendentes": 5,
  "enviadosHoje": 12,
  "comFalha": 1,
  "schedulerAtivo": true,
  "processandoAgora": false,
  "dataConsulta": "2024-01-15"
}
```

## 🎛️ Controle do Sistema

### 1. Iniciar Processamento

```bash
curl -X POST "http://localhost:8081/esocial-novo/api/emails/iniciar" \
  -H "Content-Type: application/json"
```

**Resposta:**
```json
{
  "status": "success",
  "message": "Processamento de emails iniciado"
}
```

### 2. Parar Processamento

```bash
curl -X POST "http://localhost:8081/esocial-novo/api/emails/parar" \
  -H "Content-Type: application/json"
```

**Resposta:**
```json
{
  "status": "success",
  "message": "Processamento de emails parado"
}
```

### 3. Processar Emails Imediatamente

```bash
curl -X POST "http://localhost:8081/esocial-novo/api/emails/processar-agora" \
  -H "Content-Type: application/json"
```

**Resposta:**
```json
{
  "status": "success",
  "message": "Emails processados com sucesso"
}
```

## 📧 Consulta de Emails

### 1. Listar Emails Pendentes

```bash
curl -X GET "http://localhost:8081/esocial-novo/api/emails/pendentes?page=0&size=10"
```

**Resposta:**
```json
[
  {
    "sequencia": 12345,
    "smtp": "smtplw.com.br",
    "destEmail": "<EMAIL>",
    "destNome": "Cliente Teste",
    "assunto": "Relatório Mensal",
    "flagEnviado": "",
    "dtInclusao": "2024-01-15",
    "hrInclusao": "09:30:00",
    "parametro": "SATGLOVAL"
  }
]
```

### 2. Emails Enviados Hoje

```bash
curl -X GET "http://localhost:8081/esocial-novo/api/emails/enviados-hoje"
```

### 3. Emails com Falha

```bash
curl -X GET "http://localhost:8081/esocial-novo/api/emails/com-falha?page=0&size=10"
```

### 4. Emails por Cliente

```bash
curl -X GET "http://localhost:8081/esocial-novo/api/emails/cliente/001?codFil=01&parametro=TESTE&dataInicio=2024-01-01&dataFim=2024-01-31"
```

## 🔄 Reprocessamento

### Reprocessar Email Específico

```bash
curl -X POST "http://localhost:8081/esocial-novo/api/emails/12345/reprocessar" \
  -H "Content-Type: application/json"
```

**Resposta:**
```json
{
  "status": "success",
  "message": "Email marcado para reprocessamento"
}
```

## 📊 Monitoramento com Actuator

### 1. Health Endpoint

```bash
curl -X GET "http://localhost:8081/esocial-novo/actuator/health"
```

### 2. Métricas

```bash
curl -X GET "http://localhost:8081/esocial-novo/actuator/metrics"
```

### 3. Scheduled Tasks

```bash
curl -X GET "http://localhost:8081/esocial-novo/actuator/scheduledtasks"
```

## 🐛 Troubleshooting via API

### 1. Verificar se há emails travados

```bash
# Verificar emails pendentes há muito tempo
curl -X GET "http://localhost:8081/esocial-novo/api/emails/pendentes" | jq '.[] | select(.dtInclusao < "2024-01-14")'
```

### 2. Forçar reprocessamento em lote

```bash
# Para cada email com falha, reprocessar
curl -X GET "http://localhost:8081/esocial-novo/api/emails/com-falha" | \
jq -r '.[].sequencia' | \
while read seq; do
  curl -X POST "http://localhost:8081/esocial-novo/api/emails/$seq/reprocessar"
done
```

### 3. Monitoramento contínuo

```bash
# Script para monitorar status a cada 30 segundos
while true; do
  echo "$(date): $(curl -s http://localhost:8081/esocial-novo/api/emails/estatisticas | jq '.pendentes') emails pendentes"
  sleep 30
done
```

## 📝 Exemplos com PowerShell (Windows)

### Status do Sistema

```powershell
$response = Invoke-RestMethod -Uri "http://localhost:8081/esocial-novo/api/emails/status" -Method Get
Write-Host "Scheduler ativo: $($response.ativo)"
Write-Host "Emails pendentes: $($response.emailStats.pendentes)"
```

### Iniciar Processamento

```powershell
$response = Invoke-RestMethod -Uri "http://localhost:8081/esocial-novo/api/emails/iniciar" -Method Post
Write-Host $response.message
```

### Monitoramento Contínuo

```powershell
while ($true) {
    $stats = Invoke-RestMethod -Uri "http://localhost:8081/esocial-novo/api/emails/estatisticas" -Method Get
    Write-Host "$(Get-Date): $($stats.pendentes) emails pendentes, $($stats.enviadosHoje) enviados hoje"
    Start-Sleep -Seconds 30
}
```

## 🔧 Configuração de Ambiente

### Variáveis de Ambiente

```bash
# Para desenvolvimento
export EMAIL_SCHEDULER_ENABLED=false
export LOGGING_LEVEL_COM_BR_SASW_ESOCIAL_NOVO=DEBUG

# Para produção
export EMAIL_SCHEDULER_ENABLED=true
export EMAIL_LOG_EMAILS_ENABLED=true
```

### Docker (exemplo)

```dockerfile
FROM openjdk:17-jre-slim
COPY target/esocial-novo-0.0.1-SNAPSHOT.jar app.jar
EXPOSE 8081
ENV EMAIL_SCHEDULER_ENABLED=true
ENV EMAIL_LOG_EMAILS_ENABLED=true
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 📈 Métricas Customizadas

### Prometheus (se configurado)

```bash
# Emails pendentes
curl http://localhost:8081/esocial-novo/actuator/prometheus | grep email_pendentes

# Emails enviados
curl http://localhost:8081/esocial-novo/actuator/prometheus | grep email_enviados
```

## 🚨 Alertas

### Script de Alerta Simples

```bash
#!/bin/bash
THRESHOLD=100
PENDENTES=$(curl -s http://localhost:8081/esocial-novo/api/emails/estatisticas | jq '.pendentes')

if [ "$PENDENTES" -gt "$THRESHOLD" ]; then
    echo "ALERTA: $PENDENTES emails pendentes (limite: $THRESHOLD)"
    # Enviar notificação (email, Slack, etc.)
fi
```

## 📱 Integração com Ferramentas

### Postman Collection

Importe a seguinte collection no Postman:

```json
{
  "info": {
    "name": "ESocial Novo - Email API",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "Status",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{baseUrl}}/api/emails/status",
          "host": ["{{baseUrl}}"],
          "path": ["api", "emails", "status"]
        }
      }
    },
    {
      "name": "Iniciar",
      "request": {
        "method": "POST",
        "header": [],
        "url": {
          "raw": "{{baseUrl}}/api/emails/iniciar",
          "host": ["{{baseUrl}}"],
          "path": ["api", "emails", "iniciar"]
        }
      }
    }
  ],
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:8081/esocial-novo"
    }
  ]
}
```
