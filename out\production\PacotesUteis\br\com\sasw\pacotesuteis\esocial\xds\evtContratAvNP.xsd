﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.esocial.gov.br/schema/evt/evtContratAvNP/v02_05_00" targetNamespace="http://www.esocial.gov.br/schema/evt/evtContratAvNP/v02_05_00" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
    <xs:element name="eSocial">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="evtContratAvNP">
                    <xs:annotation>
                        <xs:documentation>Remuneracao de Trab. Avulsos Nao Portuarios</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ideEvento" type="TIdeEveFopagMensal">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do evento</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="ideEmpregador" type="TEmpregador">
                                <xs:annotation>
                                    <xs:documentation>Informacoes de identificacao do empregador</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="remunAvNP" maxOccurs="999">
                                <xs:annotation>
                                    <xs:documentation>Remuneracao dos trabalhadores avulsos nao portuarios</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="tpInsc">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:byte">
                                                    <xs:pattern value="\d"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="nrInsc">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Número de Inscricao</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:pattern value="\d{8,14}"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="codLotacao">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>COdigo da Lotacao</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:string">
                                                    <xs:minLength value="1"/>
                                                    <xs:maxLength value="30"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="vrBcCp00">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Base de Calculo da Contribuicao Previdenciaria</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:decimal">
                                                    <xs:totalDigits value="14"/>
                                                    <xs:fractionDigits value="2"/>
                                                    <xs:maxInclusive value="999999999999"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="vrBcCp15">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>BC CP 15</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:decimal">
                                                    <xs:totalDigits value="14"/>
                                                    <xs:fractionDigits value="2"/>
                                                    <xs:maxInclusive value="999999999999"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="vrBcCp20">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>BC CP 20</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:decimal">
                                                    <xs:totalDigits value="14"/>
                                                    <xs:fractionDigits value="2"/>
                                                    <xs:maxInclusive value="999999999999"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="vrBcCp25">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>BC CP 25</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:decimal">
                                                    <xs:totalDigits value="14"/>
                                                    <xs:fractionDigits value="2"/>
                                                    <xs:maxInclusive value="999999999999"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="vrBcCp13">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>BC CP 13° sal</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:decimal">
                                                    <xs:totalDigits value="14"/>
                                                    <xs:fractionDigits value="2"/>
                                                    <xs:maxInclusive value="999999999999"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="vrBcFgts">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>BC FGTS</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:decimal">
                                                    <xs:totalDigits value="14"/>
                                                    <xs:fractionDigits value="2"/>
                                                    <xs:maxInclusive value="999999999999"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                        <xs:element name="vrDescCP">
                                            <xs:simpleType>
                                                <xs:annotation>
                                                    <xs:documentation>Valor da Contribuicao Descontada</xs:documentation>
                                                </xs:annotation>
                                                <xs:restriction base="xs:decimal">
                                                    <xs:totalDigits value="14"/>
                                                    <xs:fractionDigits value="2"/>
                                                    <xs:maxInclusive value="999999999999"/>
                                                </xs:restriction>
                                            </xs:simpleType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                        <xs:attribute name="Id" type="xs:ID" use="required"/>
                    </xs:complexType>
                </xs:element>
                <xs:element ref="ds:Signature"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:complexType name="TIdeEveFopagMensal">
        <xs:annotation>
            <xs:documentation>Identificacao do Evento PeriOdico</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="indRetif">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de Retificacao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrRecibo" minOccurs="0">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número do recibo do arquivo a ser retificado</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="40"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="indApuracao">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Indicativo de periodo de apuracao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="perApur">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Periodo ao qual se referem as informacoes</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="[2]{1}\d{3}-(1[0-2]|0[1-9])"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="tpAmb">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de ambiente</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="procEmi">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="verProc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Versao do processo de emissao do evento</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:minLength value="1"/>
                        <xs:maxLength value="20"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TEmpregador">
        <xs:sequence>
            <xs:element name="tpInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Tipo de Inscricao, conforme Tabela 05</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:byte">
                        <xs:pattern value="\d"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="nrInsc">
                <xs:simpleType>
                    <xs:annotation>
                        <xs:documentation>Número de Inscricao</xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                        <xs:pattern value="\d{8,14}"/>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
